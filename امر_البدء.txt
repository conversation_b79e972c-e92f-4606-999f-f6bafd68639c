claude-code "Create a comprehensive Flutter Desktop ERP system for auto parts, oils, tires, and batteries companies with the following specifications:

PROJECT NAME: auto_parts_erp

DEVELOPMENT ENVIRONMENT: Linux (targeting Windows Desktop primarily)

CORE REQUIREMENTS:
1. Flutter Desktop app targeting Windows, macOS, and Linux (focus on Windows)
2. Multi-user and multi-branch support
3. Hybrid P2P + central server architecture
4. Bilingual support (Arabic RTL + English)
5. Multiple OEM numbers support with car model linking
6. Online/Offline mode with sync (PostgreSQL central + SQLite local)
7. Shared economy system for B2B product sharing
8. WhatsApp Business API integration
9. ZATCA (Saudi tax authority) integration ready
10. Modern Material Design 3 with dark/light themes

TECHNICAL STACK:
Frontend:
- Flutter 3.x with Desktop support
- Riverpod for state management
- Drift for local SQLite database
- Dio for HTTP networking
- fl_chart for analytics
- pdf/printing for invoices
- qr_flutter for QR codes

Backend:
- Dart with Shelf framework
- PostgreSQL 15+ for central database
- Redis for caching
- JWT authentication
- WebSocket for real-time sync
- Docker compose setup

PROJECT STRUCTURE:
```
auto_parts_erp/
├── lib/
│   ├── core/
│   │   ├── constants/
│   │   ├── database/
│   │   │   ├── local/ (SQLite with Drift)
│   │   │   └── remote/ (PostgreSQL connection)
│   │   ├── localization/ (ar.json, en.json)
│   │   ├── network/
│   │   ├── routes/
│   │   ├── theme/
│   │   └── utils/
│   ├── features/
│   │   ├── auth/
│   │   ├── products/
│   │   │   ├── data/
│   │   │   ├── domain/
│   │   │   └── presentation/
│   │   ├── inventory/
│   │   ├── sales/
│   │   ├── purchases/
│   │   ├── reports/
│   │   ├── shared_economy/
│   │   ├── sync/
│   │   └── settings/
│   └── shared/
├── assets/
│   ├── images/
│   ├── fonts/ (Cairo for Arabic)
│   └── translations/
├── windows/ (Windows specific)
├── linux/ (Linux specific)
└── macos/ (macOS specific)

auto_parts_erp_backend/
├── bin/
│   └── server.dart
├── lib/
│   ├── api/
│   ├── models/
│   ├── services/
│   └── utils/
├── docker-compose.yml
├── Dockerfile
└── init.sql
```

KEY FEATURES TO IMPLEMENT:

1. PRODUCTS MODULE:
   - Product CRUD with Arabic/English names
   - Multiple OEM numbers per product
   - Car make/model/year linking
   - Barcode scanning support
   - Image gallery
   - Categories and brands management

2. INVENTORY MODULE:
   - Multi-branch inventory tracking
   - Stock transfers between branches
   - Low stock alerts
   - Batch/Serial number tracking
   - Expiry date management for oils

3. SALES MODULE:
   - POS interface optimized for desktop
   - Customer management
   - Multiple payment methods
   - Invoice generation with QR code
   - WhatsApp invoice sending
   - Returns management

4. SHARED ECONOMY:
   - B2B marketplace for sharing inventory
   - Request quotes from other companies
   - Automated stock checking across network
   - Rating system for suppliers

5. SYNC SYSTEM:
   - Automatic sync when online
   - Conflict resolution
   - Offline queue management
   - Branch-specific data isolation

6. ZATCA INTEGRATION:
   - QR code generation for invoices
   - XML invoice format
   - Digital signature
   - API integration preparation

DATABASE SCHEMA highlights:
- products table with multilingual support
- product_oem_numbers for multiple OEM
- product_car_compatibility for vehicle linking  
- branch_inventory for distributed stock
- sync_queue for offline operations
- shared_inventory for B2B features

UI/UX REQUIREMENTS:
- Responsive desktop layout
- RTL support for Arabic
- Keyboard shortcuts for common operations
- DataTable with sorting/filtering
- Dashboard with charts
- Print-optimized invoice layouts

Create the complete project with:
1. All necessary dependencies in pubspec.yaml
2. Database schema and models
3. Basic CRUD operations for products
4. Authentication system
5. Main navigation shell
6. Localization setup
7. Theme configuration
8. Docker compose for backend
9. README with setup instructions
10. Basic sync mechanism

Make it production-ready with proper error handling, logging, and performance optimization for Windows desktop deployment."