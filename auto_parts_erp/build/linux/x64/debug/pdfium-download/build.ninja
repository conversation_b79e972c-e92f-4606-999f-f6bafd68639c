# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.16

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: pdfium-download
# Configuration: 
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include rules.ninja


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-download && /snap/flutter/149/usr/bin/cmake -S/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-download -B/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-download
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-download && /snap/flutter/149/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for pdfium-download

build pdfium-download: phony CMakeFiles/pdfium-download CMakeFiles/pdfium-download-complete pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-done pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-install pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-mkdir pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-download pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-update pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-patch pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-configure pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-build pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-test


#############################################
# Phony custom command for CMakeFiles/pdfium-download

build CMakeFiles/pdfium-download: phony CMakeFiles/pdfium-download-complete


#############################################
# Custom command for CMakeFiles/pdfium-download-complete

build CMakeFiles/pdfium-download-complete pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-done: CUSTOM_COMMAND pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-install pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-mkdir pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-download pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-update pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-patch pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-configure pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-build pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-install pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-test
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-download && /snap/flutter/149/usr/bin/cmake -E make_directory /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-download/CMakeFiles && /snap/flutter/149/usr/bin/cmake -E touch /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-download/CMakeFiles/pdfium-download-complete && /snap/flutter/149/usr/bin/cmake -E touch /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-done
  DESC = Completed 'pdfium-download'
  restat = 1


#############################################
# Custom command for pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-install

build pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-install: CUSTOM_COMMAND pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-build
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-build && /snap/flutter/149/usr/bin/cmake -E echo_append && /snap/flutter/149/usr/bin/cmake -E touch /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-install
  DESC = No install step for 'pdfium-download'
  restat = 1


#############################################
# Custom command for pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-mkdir

build pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-mkdir: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-download && /snap/flutter/149/usr/bin/cmake -E make_directory /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-src && /snap/flutter/149/usr/bin/cmake -E make_directory /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-build && /snap/flutter/149/usr/bin/cmake -E make_directory /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-download/pdfium-download-prefix && /snap/flutter/149/usr/bin/cmake -E make_directory /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-download/pdfium-download-prefix/tmp && /snap/flutter/149/usr/bin/cmake -E make_directory /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp && /snap/flutter/149/usr/bin/cmake -E make_directory /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-download/pdfium-download-prefix/src && /snap/flutter/149/usr/bin/cmake -E make_directory /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp && /snap/flutter/149/usr/bin/cmake -E touch /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-mkdir
  DESC = Creating directories for 'pdfium-download'
  restat = 1


#############################################
# Custom command for pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-download

build pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-download: CUSTOM_COMMAND pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-urlinfo.txt pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-mkdir
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug && /snap/flutter/149/usr/bin/cmake -P /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/download-pdfium-download.cmake && /snap/flutter/149/usr/bin/cmake -P /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/verify-pdfium-download.cmake && /snap/flutter/149/usr/bin/cmake -P /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/extract-pdfium-download.cmake && /snap/flutter/149/usr/bin/cmake -E touch /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-download
  DESC = Performing download step (download, verify and extract) for 'pdfium-download'
  restat = 1


#############################################
# Custom command for pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-update

build pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-update: CUSTOM_COMMAND pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-download
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-download && /snap/flutter/149/usr/bin/cmake -E echo_append && /snap/flutter/149/usr/bin/cmake -E touch /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-update
  DESC = No update step for 'pdfium-download'
  restat = 1


#############################################
# Custom command for pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-patch

build pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-patch: CUSTOM_COMMAND pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-download
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-download && /snap/flutter/149/usr/bin/cmake -E echo_append && /snap/flutter/149/usr/bin/cmake -E touch /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-patch
  DESC = No patch step for 'pdfium-download'
  restat = 1


#############################################
# Custom command for pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-configure

build pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-configure: CUSTOM_COMMAND pdfium-download-prefix/tmp/pdfium-download-cfgcmd.txt pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-update pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-patch
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-build && /snap/flutter/149/usr/bin/cmake -E echo_append && /snap/flutter/149/usr/bin/cmake -E touch /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-configure
  DESC = No configure step for 'pdfium-download'
  restat = 1


#############################################
# Custom command for pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-build

build pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-build: CUSTOM_COMMAND pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-configure
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-build && /snap/flutter/149/usr/bin/cmake -E echo_append && /snap/flutter/149/usr/bin/cmake -E touch /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-build
  DESC = No build step for 'pdfium-download'
  restat = 1


#############################################
# Custom command for pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-test

build pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-test: CUSTOM_COMMAND pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-install
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-build && /snap/flutter/149/usr/bin/cmake -E echo_append && /snap/flutter/149/usr/bin/cmake -E touch /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/pdfium-download-test
  DESC = No test step for 'pdfium-download'
  restat = 1

# =============================================================================
# Target aliases.

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-download

build all: phony pdfium-download

# =============================================================================
# Built-in targets


#############################################
# Make the all target the default.

default all

#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeDetermineSystem.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeGenericSystem.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeInitializeConfigs.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeSystem.cmake.in /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeSystemSpecificInformation.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeSystemSpecificInitialize.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/ExternalProject-download.cmake.in /snap/flutter/149/usr/share/cmake-3.16/Modules/ExternalProject.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/UnixPaths.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/RepositoryInfo.txt.in CMakeCache.txt CMakeFiles/3.16.3/CMakeSystem.cmake CMakeLists.txt pdfium-download-prefix/tmp/pdfium-download-cfgcmd.txt.in
  pool = console


#############################################
# A missing CMake input file is not an error.

build /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeDetermineSystem.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeGenericSystem.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeInitializeConfigs.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeSystem.cmake.in /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeSystemSpecificInformation.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeSystemSpecificInitialize.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/ExternalProject-download.cmake.in /snap/flutter/149/usr/share/cmake-3.16/Modules/ExternalProject.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/UnixPaths.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/RepositoryInfo.txt.in CMakeCache.txt CMakeFiles/3.16.3/CMakeSystem.cmake CMakeLists.txt pdfium-download-prefix/tmp/pdfium-download-cfgcmd.txt.in: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP

