# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.16

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: runner
# Configuration: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include rules.ninja


#############################################
# Utility command for install/strip

build CMakeFiles/install/strip.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build install/strip: phony CMakeFiles/install/strip.util


#############################################
# Utility command for install/local

build CMakeFiles/install/local.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build install/local: phony CMakeFiles/install/local.util


#############################################
# Utility command for install

build CMakeFiles/install.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug && /snap/flutter/149/usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build install: phony CMakeFiles/install.util


#############################################
# Utility command for list_install_components

build list_install_components: phony


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug && /snap/flutter/149/usr/bin/cmake -S/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux -B/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug && /snap/flutter/149/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for install/strip

build flutter/CMakeFiles/install/strip.util: CUSTOM_COMMAND flutter/all
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/flutter && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build flutter/install/strip: phony flutter/CMakeFiles/install/strip.util


#############################################
# Utility command for install/local

build flutter/CMakeFiles/install/local.util: CUSTOM_COMMAND flutter/all
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/flutter && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build flutter/install/local: phony flutter/CMakeFiles/install/local.util


#############################################
# Utility command for install

build flutter/CMakeFiles/install.util: CUSTOM_COMMAND flutter/all
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/flutter && /snap/flutter/149/usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build flutter/install: phony flutter/CMakeFiles/install.util


#############################################
# Utility command for list_install_components

build flutter/list_install_components: phony


#############################################
# Utility command for rebuild_cache

build flutter/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/flutter && /snap/flutter/149/usr/bin/cmake -S/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux -B/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build flutter/rebuild_cache: phony flutter/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for edit_cache

build flutter/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/flutter && /snap/flutter/149/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build flutter/edit_cache: phony flutter/CMakeFiles/edit_cache.util


#############################################
# Utility command for flutter_assemble

build flutter/flutter_assemble: phony flutter/CMakeFiles/flutter_assemble /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/libflutter_linux_gtk.so /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_basic_message_channel.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_binary_codec.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_binary_messenger.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_dart_project.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_engine.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_json_message_codec.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_json_method_codec.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_message_codec.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_method_call.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_method_channel.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_method_codec.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_method_response.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_plugin_registrar.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_plugin_registry.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_standard_message_codec.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_standard_method_codec.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_string_codec.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_value.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_view.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/flutter_linux.h flutter/_phony_


#############################################
# Phony custom command for flutter/CMakeFiles/flutter_assemble

build flutter/CMakeFiles/flutter_assemble: phony /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/libflutter_linux_gtk.so /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_basic_message_channel.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_binary_codec.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_binary_messenger.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_dart_project.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_engine.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_json_message_codec.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_json_method_codec.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_message_codec.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_method_call.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_method_channel.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_method_codec.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_method_response.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_plugin_registrar.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_plugin_registry.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_standard_message_codec.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_standard_method_codec.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_string_codec.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_value.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_view.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/flutter_linux.h


#############################################
# Custom command for /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/libflutter_linux_gtk.so

build /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/libflutter_linux_gtk.so /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_basic_message_channel.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_binary_codec.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_binary_messenger.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_dart_project.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_engine.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_json_message_codec.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_json_method_codec.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_message_codec.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_method_call.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_method_channel.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_method_codec.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_method_response.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_plugin_registrar.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_plugin_registry.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_standard_message_codec.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_standard_method_codec.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_string_codec.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_value.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_view.h /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/flutter_linux.h flutter/_phony_: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/flutter && /snap/flutter/149/usr/bin/cmake -E env FLUTTER_ROOT=/home/<USER>/snap/flutter/common/flutter PROJECT_DIR=/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuNQ==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049ZmNmMmMxMTU3Mg==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049ZGQ5M2RlNmZiMQ==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE= DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/.dart_tool/package_config.json FLUTTER_TARGET=/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/lib/main_app.dart /home/<USER>/snap/flutter/common/flutter/packages/flutter_tools/bin/tool_backend.sh linux-x64 Debug
  DESC = Generating /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/libflutter_linux_gtk.so, /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_basic_message_channel.h, /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_binary_codec.h, /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_binary_messenger.h, /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_dart_project.h, /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_engine.h, /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_json_message_codec.h, /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_json_method_codec.h, /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_message_codec.h, /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_method_call.h, /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_method_channel.h, /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_method_codec.h, /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_method_response.h, /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_plugin_registrar.h, /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_plugin_registry.h, /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_standard_message_codec.h, /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_standard_method_codec.h, /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_string_codec.h, /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_value.h, /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/fl_view.h, /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/flutter_linux/flutter_linux.h, _phony_
  restat = 1

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for install/strip

build runner/CMakeFiles/install/strip.util: CUSTOM_COMMAND runner/all
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/runner && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build runner/install/strip: phony runner/CMakeFiles/install/strip.util


#############################################
# Utility command for install

build runner/CMakeFiles/install.util: CUSTOM_COMMAND runner/all
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/runner && /snap/flutter/149/usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build runner/install: phony runner/CMakeFiles/install.util


#############################################
# Utility command for list_install_components

build runner/list_install_components: phony


#############################################
# Utility command for rebuild_cache

build runner/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/runner && /snap/flutter/149/usr/bin/cmake -S/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux -B/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build runner/rebuild_cache: phony runner/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for edit_cache

build runner/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/runner && /snap/flutter/149/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build runner/edit_cache: phony runner/CMakeFiles/edit_cache.util


#############################################
# Utility command for install/local

build runner/CMakeFiles/install/local.util: CUSTOM_COMMAND runner/all
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/runner && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build runner/install/local: phony runner/CMakeFiles/install/local.util

# =============================================================================
# Object build statements for EXECUTABLE target auto_parts_erp


#############################################
# Order-only phony target for auto_parts_erp

build cmake_object_order_depends_target_auto_parts_erp: phony || cmake_object_order_depends_target_file_selector_linux_plugin cmake_object_order_depends_target_printing_plugin cmake_object_order_depends_target_screen_retriever_plugin cmake_object_order_depends_target_sqlite3_flutter_libs_plugin cmake_object_order_depends_target_window_manager_plugin flutter/flutter_assemble

build runner/CMakeFiles/auto_parts_erp.dir/main.cc.o: CXX_COMPILER__auto_parts_erp /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/runner/main.cc || cmake_object_order_depends_target_auto_parts_erp
  DEFINES = -DAPPLICATION_ID=\"com.example.auto_parts_erp\"
  DEP_FILE = runner/CMakeFiles/auto_parts_erp.dir/main.cc.o.d
  FLAGS = -g   -Wall -Werror -pthread
  INCLUDES = -I/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux -I/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral -I/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/file_selector_linux/linux/include -I/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/printing/linux/include -I/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/screen_retriever/linux/include -I/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/sqlite3_flutter_libs/linux/include -I/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/window_manager/linux/include -isystem /snap/flutter/current/usr/include/gtk-3.0 -isystem /snap/flutter/current/usr/include/at-spi2-atk/2.0 -isystem /snap/flutter/current/usr/include/at-spi-2.0 -isystem /snap/flutter/current/usr/include/dbus-1.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/dbus-1.0/include -isystem /snap/flutter/current/usr/include/gio-unix-2.0 -isystem /snap/flutter/current/usr/include/cairo -isystem /snap/flutter/current/usr/include/pango-1.0 -isystem /snap/flutter/current/usr/include/fribidi -isystem /snap/flutter/current/usr/include/harfbuzz -isystem /snap/flutter/current/usr/include/atk-1.0 -isystem /snap/flutter/current/usr/include/pixman-1 -isystem /snap/flutter/current/usr/include/uuid -isystem /snap/flutter/current/usr/include/freetype2 -isystem /snap/flutter/current/usr/include/libpng16 -isystem /snap/flutter/current/usr/include/gdk-pixbuf-2.0 -isystem /snap/flutter/current/usr/include/libmount -isystem /snap/flutter/current/usr/include/blkid -isystem /snap/flutter/current/usr/include/glib-2.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
  OBJECT_DIR = runner/CMakeFiles/auto_parts_erp.dir
  OBJECT_FILE_DIR = runner/CMakeFiles/auto_parts_erp.dir

build runner/CMakeFiles/auto_parts_erp.dir/my_application.cc.o: CXX_COMPILER__auto_parts_erp /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/runner/my_application.cc || cmake_object_order_depends_target_auto_parts_erp
  DEFINES = -DAPPLICATION_ID=\"com.example.auto_parts_erp\"
  DEP_FILE = runner/CMakeFiles/auto_parts_erp.dir/my_application.cc.o.d
  FLAGS = -g   -Wall -Werror -pthread
  INCLUDES = -I/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux -I/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral -I/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/file_selector_linux/linux/include -I/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/printing/linux/include -I/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/screen_retriever/linux/include -I/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/sqlite3_flutter_libs/linux/include -I/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/window_manager/linux/include -isystem /snap/flutter/current/usr/include/gtk-3.0 -isystem /snap/flutter/current/usr/include/at-spi2-atk/2.0 -isystem /snap/flutter/current/usr/include/at-spi-2.0 -isystem /snap/flutter/current/usr/include/dbus-1.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/dbus-1.0/include -isystem /snap/flutter/current/usr/include/gio-unix-2.0 -isystem /snap/flutter/current/usr/include/cairo -isystem /snap/flutter/current/usr/include/pango-1.0 -isystem /snap/flutter/current/usr/include/fribidi -isystem /snap/flutter/current/usr/include/harfbuzz -isystem /snap/flutter/current/usr/include/atk-1.0 -isystem /snap/flutter/current/usr/include/pixman-1 -isystem /snap/flutter/current/usr/include/uuid -isystem /snap/flutter/current/usr/include/freetype2 -isystem /snap/flutter/current/usr/include/libpng16 -isystem /snap/flutter/current/usr/include/gdk-pixbuf-2.0 -isystem /snap/flutter/current/usr/include/libmount -isystem /snap/flutter/current/usr/include/blkid -isystem /snap/flutter/current/usr/include/glib-2.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
  OBJECT_DIR = runner/CMakeFiles/auto_parts_erp.dir
  OBJECT_FILE_DIR = runner/CMakeFiles/auto_parts_erp.dir

build runner/CMakeFiles/auto_parts_erp.dir/__/flutter/generated_plugin_registrant.cc.o: CXX_COMPILER__auto_parts_erp /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/generated_plugin_registrant.cc || cmake_object_order_depends_target_auto_parts_erp
  DEFINES = -DAPPLICATION_ID=\"com.example.auto_parts_erp\"
  DEP_FILE = runner/CMakeFiles/auto_parts_erp.dir/__/flutter/generated_plugin_registrant.cc.o.d
  FLAGS = -g   -Wall -Werror -pthread
  INCLUDES = -I/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux -I/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral -I/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/file_selector_linux/linux/include -I/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/printing/linux/include -I/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/screen_retriever/linux/include -I/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/sqlite3_flutter_libs/linux/include -I/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/window_manager/linux/include -isystem /snap/flutter/current/usr/include/gtk-3.0 -isystem /snap/flutter/current/usr/include/at-spi2-atk/2.0 -isystem /snap/flutter/current/usr/include/at-spi-2.0 -isystem /snap/flutter/current/usr/include/dbus-1.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/dbus-1.0/include -isystem /snap/flutter/current/usr/include/gio-unix-2.0 -isystem /snap/flutter/current/usr/include/cairo -isystem /snap/flutter/current/usr/include/pango-1.0 -isystem /snap/flutter/current/usr/include/fribidi -isystem /snap/flutter/current/usr/include/harfbuzz -isystem /snap/flutter/current/usr/include/atk-1.0 -isystem /snap/flutter/current/usr/include/pixman-1 -isystem /snap/flutter/current/usr/include/uuid -isystem /snap/flutter/current/usr/include/freetype2 -isystem /snap/flutter/current/usr/include/libpng16 -isystem /snap/flutter/current/usr/include/gdk-pixbuf-2.0 -isystem /snap/flutter/current/usr/include/libmount -isystem /snap/flutter/current/usr/include/blkid -isystem /snap/flutter/current/usr/include/glib-2.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
  OBJECT_DIR = runner/CMakeFiles/auto_parts_erp.dir
  OBJECT_FILE_DIR = runner/CMakeFiles/auto_parts_erp.dir/__/flutter


# =============================================================================
# Link build statements for EXECUTABLE target auto_parts_erp


#############################################
# Link the executable intermediates_do_not_run/auto_parts_erp

build intermediates_do_not_run/auto_parts_erp: CXX_EXECUTABLE_LINKER__auto_parts_erp runner/CMakeFiles/auto_parts_erp.dir/main.cc.o runner/CMakeFiles/auto_parts_erp.dir/my_application.cc.o runner/CMakeFiles/auto_parts_erp.dir/__/flutter/generated_plugin_registrant.cc.o | plugins/file_selector_linux/libfile_selector_linux_plugin.so plugins/printing/libprinting_plugin.so plugins/screen_retriever/libscreen_retriever_plugin.so plugins/sqlite3_flutter_libs/libsqlite3_flutter_libs_plugin.so plugins/window_manager/libwindow_manager_plugin.so /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/libflutter_linux_gtk.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgtk-3.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk-3.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpango-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libharfbuzz.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libatk-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo-gobject.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgio-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgobject-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libglib-2.0.so || flutter/flutter_assemble plugins/file_selector_linux/libfile_selector_linux_plugin.so plugins/printing/libprinting_plugin.so plugins/screen_retriever/libscreen_retriever_plugin.so plugins/sqlite3_flutter_libs/libsqlite3_flutter_libs_plugin.so plugins/window_manager/libwindow_manager_plugin.so
  FLAGS = -g
  LINK_FLAGS = -B/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -B/snap/flutter/current/usr/lib/x86_64-linux-gnu -B/snap/flutter/current/lib/x86_64-linux-gnu -B/snap/flutter/current/usr/lib/ -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib/ -lblkid -lgcrypt -llzma -llz4 -lgpg-error -luuid -lpthread -ldl -lepoxy -lfontconfig
  LINK_LIBRARIES = -Wl,-rpath,/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/file_selector_linux:/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/printing:/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/screen_retriever:/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/sqlite3_flutter_libs:/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/window_manager:/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral:/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-src/lib:  plugins/file_selector_linux/libfile_selector_linux_plugin.so  plugins/printing/libprinting_plugin.so  plugins/screen_retriever/libscreen_retriever_plugin.so  plugins/sqlite3_flutter_libs/libsqlite3_flutter_libs_plugin.so  plugins/window_manager/libwindow_manager_plugin.so  /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/libflutter_linux_gtk.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgtk-3.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk-3.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpango-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libharfbuzz.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libatk-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo-gobject.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgio-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgobject-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libglib-2.0.so  -Wl,-rpath-link,/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-src/lib
  OBJECT_DIR = runner/CMakeFiles/auto_parts_erp.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = intermediates_do_not_run/auto_parts_erp
  TARGET_PDB = auto_parts_erp.dbg

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/generated_plugins.cmake
# =============================================================================


#############################################
# Utility command for install/strip

build plugins/file_selector_linux/CMakeFiles/install/strip.util: CUSTOM_COMMAND plugins/file_selector_linux/all
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/file_selector_linux && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build plugins/file_selector_linux/install/strip: phony plugins/file_selector_linux/CMakeFiles/install/strip.util


#############################################
# Utility command for install/local

build plugins/file_selector_linux/CMakeFiles/install/local.util: CUSTOM_COMMAND plugins/file_selector_linux/all
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/file_selector_linux && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build plugins/file_selector_linux/install/local: phony plugins/file_selector_linux/CMakeFiles/install/local.util


#############################################
# Utility command for install

build plugins/file_selector_linux/CMakeFiles/install.util: CUSTOM_COMMAND plugins/file_selector_linux/all
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/file_selector_linux && /snap/flutter/149/usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build plugins/file_selector_linux/install: phony plugins/file_selector_linux/CMakeFiles/install.util


#############################################
# Utility command for list_install_components

build plugins/file_selector_linux/list_install_components: phony


#############################################
# Utility command for rebuild_cache

build plugins/file_selector_linux/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/file_selector_linux && /snap/flutter/149/usr/bin/cmake -S/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux -B/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build plugins/file_selector_linux/rebuild_cache: phony plugins/file_selector_linux/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for edit_cache

build plugins/file_selector_linux/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/file_selector_linux && /snap/flutter/149/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build plugins/file_selector_linux/edit_cache: phony plugins/file_selector_linux/CMakeFiles/edit_cache.util

# =============================================================================
# Object build statements for SHARED_LIBRARY target file_selector_linux_plugin


#############################################
# Order-only phony target for file_selector_linux_plugin

build cmake_object_order_depends_target_file_selector_linux_plugin: phony || flutter/flutter_assemble

build plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir/file_selector_plugin.cc.o: CXX_COMPILER__file_selector_linux_plugin /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/file_selector_linux/linux/file_selector_plugin.cc || cmake_object_order_depends_target_file_selector_linux_plugin
  DEFINES = -DFLUTTER_PLUGIN_IMPL -Dfile_selector_linux_plugin_EXPORTS
  DEP_FILE = plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir/file_selector_plugin.cc.o.d
  FLAGS = -g -fPIC -fvisibility=hidden   -Wall -Werror -pthread
  INCLUDES = -I/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral -isystem /snap/flutter/current/usr/include/gtk-3.0 -isystem /snap/flutter/current/usr/include/at-spi2-atk/2.0 -isystem /snap/flutter/current/usr/include/at-spi-2.0 -isystem /snap/flutter/current/usr/include/dbus-1.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/dbus-1.0/include -isystem /snap/flutter/current/usr/include/gio-unix-2.0 -isystem /snap/flutter/current/usr/include/cairo -isystem /snap/flutter/current/usr/include/pango-1.0 -isystem /snap/flutter/current/usr/include/fribidi -isystem /snap/flutter/current/usr/include/harfbuzz -isystem /snap/flutter/current/usr/include/atk-1.0 -isystem /snap/flutter/current/usr/include/pixman-1 -isystem /snap/flutter/current/usr/include/uuid -isystem /snap/flutter/current/usr/include/freetype2 -isystem /snap/flutter/current/usr/include/libpng16 -isystem /snap/flutter/current/usr/include/gdk-pixbuf-2.0 -isystem /snap/flutter/current/usr/include/libmount -isystem /snap/flutter/current/usr/include/blkid -isystem /snap/flutter/current/usr/include/glib-2.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
  OBJECT_DIR = plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir
  OBJECT_FILE_DIR = plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir

build plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir/messages.g.cc.o: CXX_COMPILER__file_selector_linux_plugin /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/file_selector_linux/linux/messages.g.cc || cmake_object_order_depends_target_file_selector_linux_plugin
  DEFINES = -DFLUTTER_PLUGIN_IMPL -Dfile_selector_linux_plugin_EXPORTS
  DEP_FILE = plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir/messages.g.cc.o.d
  FLAGS = -g -fPIC -fvisibility=hidden   -Wall -Werror -pthread
  INCLUDES = -I/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral -isystem /snap/flutter/current/usr/include/gtk-3.0 -isystem /snap/flutter/current/usr/include/at-spi2-atk/2.0 -isystem /snap/flutter/current/usr/include/at-spi-2.0 -isystem /snap/flutter/current/usr/include/dbus-1.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/dbus-1.0/include -isystem /snap/flutter/current/usr/include/gio-unix-2.0 -isystem /snap/flutter/current/usr/include/cairo -isystem /snap/flutter/current/usr/include/pango-1.0 -isystem /snap/flutter/current/usr/include/fribidi -isystem /snap/flutter/current/usr/include/harfbuzz -isystem /snap/flutter/current/usr/include/atk-1.0 -isystem /snap/flutter/current/usr/include/pixman-1 -isystem /snap/flutter/current/usr/include/uuid -isystem /snap/flutter/current/usr/include/freetype2 -isystem /snap/flutter/current/usr/include/libpng16 -isystem /snap/flutter/current/usr/include/gdk-pixbuf-2.0 -isystem /snap/flutter/current/usr/include/libmount -isystem /snap/flutter/current/usr/include/blkid -isystem /snap/flutter/current/usr/include/glib-2.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
  OBJECT_DIR = plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir
  OBJECT_FILE_DIR = plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir


# =============================================================================
# Link build statements for SHARED_LIBRARY target file_selector_linux_plugin


#############################################
# Link the shared library plugins/file_selector_linux/libfile_selector_linux_plugin.so

build plugins/file_selector_linux/libfile_selector_linux_plugin.so: CXX_SHARED_LIBRARY_LINKER__file_selector_linux_plugin plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir/file_selector_plugin.cc.o plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir/messages.g.cc.o | /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/libflutter_linux_gtk.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgtk-3.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk-3.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpango-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libharfbuzz.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libatk-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo-gobject.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgio-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgobject-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libglib-2.0.so || flutter/flutter_assemble
  LANGUAGE_COMPILE_FLAGS = -g
  LINK_FLAGS = -B/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -B/snap/flutter/current/usr/lib/x86_64-linux-gnu -B/snap/flutter/current/lib/x86_64-linux-gnu -B/snap/flutter/current/usr/lib/ -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib/ -lblkid -lgcrypt -llzma -llz4 -lgpg-error -luuid -lpthread -ldl -lepoxy -lfontconfig
  LINK_LIBRARIES = -Wl,-rpath,/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral  /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/libflutter_linux_gtk.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgtk-3.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk-3.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpango-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libharfbuzz.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libatk-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo-gobject.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgio-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgobject-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libglib-2.0.so
  OBJECT_DIR = plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libfile_selector_linux_plugin.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = plugins/file_selector_linux/libfile_selector_linux_plugin.so
  TARGET_PDB = file_selector_linux_plugin.so.dbg

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/generated_plugins.cmake
# =============================================================================


#############################################
# Utility command for install/strip

build plugins/printing/CMakeFiles/install/strip.util: CUSTOM_COMMAND plugins/printing/all
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/printing && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build plugins/printing/install/strip: phony plugins/printing/CMakeFiles/install/strip.util


#############################################
# Utility command for install/local

build plugins/printing/CMakeFiles/install/local.util: CUSTOM_COMMAND plugins/printing/all
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/printing && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build plugins/printing/install/local: phony plugins/printing/CMakeFiles/install/local.util


#############################################
# Utility command for install

build plugins/printing/CMakeFiles/install.util: CUSTOM_COMMAND plugins/printing/all
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/printing && /snap/flutter/149/usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build plugins/printing/install: phony plugins/printing/CMakeFiles/install.util


#############################################
# Utility command for list_install_components

build plugins/printing/list_install_components: phony


#############################################
# Utility command for rebuild_cache

build plugins/printing/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/printing && /snap/flutter/149/usr/bin/cmake -S/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux -B/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build plugins/printing/rebuild_cache: phony plugins/printing/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for edit_cache

build plugins/printing/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/printing && /snap/flutter/149/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build plugins/printing/edit_cache: phony plugins/printing/CMakeFiles/edit_cache.util

# =============================================================================
# Object build statements for SHARED_LIBRARY target printing_plugin


#############################################
# Order-only phony target for printing_plugin

build cmake_object_order_depends_target_printing_plugin: phony || flutter/flutter_assemble

build plugins/printing/CMakeFiles/printing_plugin.dir/printing_plugin.cc.o: CXX_COMPILER__printing_plugin /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/printing/linux/printing_plugin.cc || cmake_object_order_depends_target_printing_plugin
  DEFINES = -DFLUTTER_PLUGIN_IMPL -Dprinting_plugin_EXPORTS
  DEP_FILE = plugins/printing/CMakeFiles/printing_plugin.dir/printing_plugin.cc.o.d
  FLAGS = -g -fPIC -fvisibility=hidden   -Wall -Werror -pthread
  INCLUDES = -I/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral -isystem /snap/flutter/current/usr/include/gtk-3.0 -isystem /snap/flutter/current/usr/include/at-spi2-atk/2.0 -isystem /snap/flutter/current/usr/include/at-spi-2.0 -isystem /snap/flutter/current/usr/include/dbus-1.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/dbus-1.0/include -isystem /snap/flutter/current/usr/include/gio-unix-2.0 -isystem /snap/flutter/current/usr/include/cairo -isystem /snap/flutter/current/usr/include/pango-1.0 -isystem /snap/flutter/current/usr/include/fribidi -isystem /snap/flutter/current/usr/include/harfbuzz -isystem /snap/flutter/current/usr/include/atk-1.0 -isystem /snap/flutter/current/usr/include/pixman-1 -isystem /snap/flutter/current/usr/include/uuid -isystem /snap/flutter/current/usr/include/freetype2 -isystem /snap/flutter/current/usr/include/libpng16 -isystem /snap/flutter/current/usr/include/gdk-pixbuf-2.0 -isystem /snap/flutter/current/usr/include/libmount -isystem /snap/flutter/current/usr/include/blkid -isystem /snap/flutter/current/usr/include/glib-2.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include -isystem /snap/flutter/current/usr/include/gtk-3.0/unix-print -isystem pdfium-src/include -isystem pdfium-src/include/cpp
  OBJECT_DIR = plugins/printing/CMakeFiles/printing_plugin.dir
  OBJECT_FILE_DIR = plugins/printing/CMakeFiles/printing_plugin.dir

build plugins/printing/CMakeFiles/printing_plugin.dir/print_job.cc.o: CXX_COMPILER__printing_plugin /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/printing/linux/print_job.cc || cmake_object_order_depends_target_printing_plugin
  DEFINES = -DFLUTTER_PLUGIN_IMPL -Dprinting_plugin_EXPORTS
  DEP_FILE = plugins/printing/CMakeFiles/printing_plugin.dir/print_job.cc.o.d
  FLAGS = -g -fPIC -fvisibility=hidden   -Wall -Werror -pthread
  INCLUDES = -I/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral -isystem /snap/flutter/current/usr/include/gtk-3.0 -isystem /snap/flutter/current/usr/include/at-spi2-atk/2.0 -isystem /snap/flutter/current/usr/include/at-spi-2.0 -isystem /snap/flutter/current/usr/include/dbus-1.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/dbus-1.0/include -isystem /snap/flutter/current/usr/include/gio-unix-2.0 -isystem /snap/flutter/current/usr/include/cairo -isystem /snap/flutter/current/usr/include/pango-1.0 -isystem /snap/flutter/current/usr/include/fribidi -isystem /snap/flutter/current/usr/include/harfbuzz -isystem /snap/flutter/current/usr/include/atk-1.0 -isystem /snap/flutter/current/usr/include/pixman-1 -isystem /snap/flutter/current/usr/include/uuid -isystem /snap/flutter/current/usr/include/freetype2 -isystem /snap/flutter/current/usr/include/libpng16 -isystem /snap/flutter/current/usr/include/gdk-pixbuf-2.0 -isystem /snap/flutter/current/usr/include/libmount -isystem /snap/flutter/current/usr/include/blkid -isystem /snap/flutter/current/usr/include/glib-2.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include -isystem /snap/flutter/current/usr/include/gtk-3.0/unix-print -isystem pdfium-src/include -isystem pdfium-src/include/cpp
  OBJECT_DIR = plugins/printing/CMakeFiles/printing_plugin.dir
  OBJECT_FILE_DIR = plugins/printing/CMakeFiles/printing_plugin.dir


# =============================================================================
# Link build statements for SHARED_LIBRARY target printing_plugin


#############################################
# Link the shared library plugins/printing/libprinting_plugin.so

build plugins/printing/libprinting_plugin.so: CXX_SHARED_LIBRARY_LINKER__printing_plugin plugins/printing/CMakeFiles/printing_plugin.dir/printing_plugin.cc.o plugins/printing/CMakeFiles/printing_plugin.dir/print_job.cc.o | pdfium-src/lib/libpdfium.so /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/libflutter_linux_gtk.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgtk-3.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk-3.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpango-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libharfbuzz.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libatk-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo-gobject.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgio-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgobject-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libglib-2.0.so || flutter/flutter_assemble
  LANGUAGE_COMPILE_FLAGS = -g
  LINK_FLAGS = -B/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -B/snap/flutter/current/usr/lib/x86_64-linux-gnu -B/snap/flutter/current/lib/x86_64-linux-gnu -B/snap/flutter/current/usr/lib/ -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib/ -lblkid -lgcrypt -llzma -llz4 -lgpg-error -luuid -lpthread -ldl -lepoxy -lfontconfig
  LINK_LIBRARIES = -Wl,-rpath,"\$$ORIGIN:/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/pdfium-src/lib"  pdfium-src/lib/libpdfium.so  /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/libflutter_linux_gtk.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgtk-3.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk-3.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpango-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libharfbuzz.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libatk-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo-gobject.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgio-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgobject-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libglib-2.0.so
  OBJECT_DIR = plugins/printing/CMakeFiles/printing_plugin.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libprinting_plugin.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = plugins/printing/libprinting_plugin.so
  TARGET_PDB = printing_plugin.so.dbg

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/generated_plugins.cmake
# =============================================================================


#############################################
# Utility command for install/strip

build plugins/screen_retriever/CMakeFiles/install/strip.util: CUSTOM_COMMAND plugins/screen_retriever/all
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/screen_retriever && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build plugins/screen_retriever/install/strip: phony plugins/screen_retriever/CMakeFiles/install/strip.util


#############################################
# Utility command for install/local

build plugins/screen_retriever/CMakeFiles/install/local.util: CUSTOM_COMMAND plugins/screen_retriever/all
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/screen_retriever && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build plugins/screen_retriever/install/local: phony plugins/screen_retriever/CMakeFiles/install/local.util


#############################################
# Utility command for install

build plugins/screen_retriever/CMakeFiles/install.util: CUSTOM_COMMAND plugins/screen_retriever/all
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/screen_retriever && /snap/flutter/149/usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build plugins/screen_retriever/install: phony plugins/screen_retriever/CMakeFiles/install.util


#############################################
# Utility command for list_install_components

build plugins/screen_retriever/list_install_components: phony


#############################################
# Utility command for rebuild_cache

build plugins/screen_retriever/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/screen_retriever && /snap/flutter/149/usr/bin/cmake -S/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux -B/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build plugins/screen_retriever/rebuild_cache: phony plugins/screen_retriever/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for edit_cache

build plugins/screen_retriever/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/screen_retriever && /snap/flutter/149/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build plugins/screen_retriever/edit_cache: phony plugins/screen_retriever/CMakeFiles/edit_cache.util

# =============================================================================
# Object build statements for SHARED_LIBRARY target screen_retriever_plugin


#############################################
# Order-only phony target for screen_retriever_plugin

build cmake_object_order_depends_target_screen_retriever_plugin: phony || flutter/flutter_assemble

build plugins/screen_retriever/CMakeFiles/screen_retriever_plugin.dir/screen_retriever_plugin.cc.o: CXX_COMPILER__screen_retriever_plugin /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/screen_retriever/linux/screen_retriever_plugin.cc || cmake_object_order_depends_target_screen_retriever_plugin
  DEFINES = -DFLUTTER_PLUGIN_IMPL -Dscreen_retriever_plugin_EXPORTS
  DEP_FILE = plugins/screen_retriever/CMakeFiles/screen_retriever_plugin.dir/screen_retriever_plugin.cc.o.d
  FLAGS = -g -fPIC -fvisibility=hidden   -Wall -Werror -pthread
  INCLUDES = -I/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral -isystem /snap/flutter/current/usr/include/gtk-3.0 -isystem /snap/flutter/current/usr/include/at-spi2-atk/2.0 -isystem /snap/flutter/current/usr/include/at-spi-2.0 -isystem /snap/flutter/current/usr/include/dbus-1.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/dbus-1.0/include -isystem /snap/flutter/current/usr/include/gio-unix-2.0 -isystem /snap/flutter/current/usr/include/cairo -isystem /snap/flutter/current/usr/include/pango-1.0 -isystem /snap/flutter/current/usr/include/fribidi -isystem /snap/flutter/current/usr/include/harfbuzz -isystem /snap/flutter/current/usr/include/atk-1.0 -isystem /snap/flutter/current/usr/include/pixman-1 -isystem /snap/flutter/current/usr/include/uuid -isystem /snap/flutter/current/usr/include/freetype2 -isystem /snap/flutter/current/usr/include/libpng16 -isystem /snap/flutter/current/usr/include/gdk-pixbuf-2.0 -isystem /snap/flutter/current/usr/include/libmount -isystem /snap/flutter/current/usr/include/blkid -isystem /snap/flutter/current/usr/include/glib-2.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
  OBJECT_DIR = plugins/screen_retriever/CMakeFiles/screen_retriever_plugin.dir
  OBJECT_FILE_DIR = plugins/screen_retriever/CMakeFiles/screen_retriever_plugin.dir


# =============================================================================
# Link build statements for SHARED_LIBRARY target screen_retriever_plugin


#############################################
# Link the shared library plugins/screen_retriever/libscreen_retriever_plugin.so

build plugins/screen_retriever/libscreen_retriever_plugin.so: CXX_SHARED_LIBRARY_LINKER__screen_retriever_plugin plugins/screen_retriever/CMakeFiles/screen_retriever_plugin.dir/screen_retriever_plugin.cc.o | /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/libflutter_linux_gtk.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgtk-3.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk-3.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpango-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libharfbuzz.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libatk-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo-gobject.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgio-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgobject-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libglib-2.0.so || flutter/flutter_assemble
  LANGUAGE_COMPILE_FLAGS = -g
  LINK_FLAGS = -B/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -B/snap/flutter/current/usr/lib/x86_64-linux-gnu -B/snap/flutter/current/lib/x86_64-linux-gnu -B/snap/flutter/current/usr/lib/ -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib/ -lblkid -lgcrypt -llzma -llz4 -lgpg-error -luuid -lpthread -ldl -lepoxy -lfontconfig
  LINK_LIBRARIES = -Wl,-rpath,/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral  /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/libflutter_linux_gtk.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgtk-3.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk-3.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpango-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libharfbuzz.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libatk-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo-gobject.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgio-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgobject-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libglib-2.0.so
  OBJECT_DIR = plugins/screen_retriever/CMakeFiles/screen_retriever_plugin.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libscreen_retriever_plugin.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = plugins/screen_retriever/libscreen_retriever_plugin.so
  TARGET_PDB = screen_retriever_plugin.so.dbg

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/generated_plugins.cmake
# =============================================================================


#############################################
# Utility command for install/strip

build plugins/sqlite3_flutter_libs/CMakeFiles/install/strip.util: CUSTOM_COMMAND plugins/sqlite3_flutter_libs/all
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/sqlite3_flutter_libs && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build plugins/sqlite3_flutter_libs/install/strip: phony plugins/sqlite3_flutter_libs/CMakeFiles/install/strip.util


#############################################
# Utility command for install/local

build plugins/sqlite3_flutter_libs/CMakeFiles/install/local.util: CUSTOM_COMMAND plugins/sqlite3_flutter_libs/all
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/sqlite3_flutter_libs && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build plugins/sqlite3_flutter_libs/install/local: phony plugins/sqlite3_flutter_libs/CMakeFiles/install/local.util


#############################################
# Utility command for edit_cache

build plugins/sqlite3_flutter_libs/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/sqlite3_flutter_libs && /snap/flutter/149/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build plugins/sqlite3_flutter_libs/edit_cache: phony plugins/sqlite3_flutter_libs/CMakeFiles/edit_cache.util


#############################################
# Utility command for install

build plugins/sqlite3_flutter_libs/CMakeFiles/install.util: CUSTOM_COMMAND plugins/sqlite3_flutter_libs/all
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/sqlite3_flutter_libs && /snap/flutter/149/usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build plugins/sqlite3_flutter_libs/install: phony plugins/sqlite3_flutter_libs/CMakeFiles/install.util


#############################################
# Utility command for list_install_components

build plugins/sqlite3_flutter_libs/list_install_components: phony


#############################################
# Utility command for rebuild_cache

build plugins/sqlite3_flutter_libs/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/sqlite3_flutter_libs && /snap/flutter/149/usr/bin/cmake -S/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux -B/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build plugins/sqlite3_flutter_libs/rebuild_cache: phony plugins/sqlite3_flutter_libs/CMakeFiles/rebuild_cache.util

# =============================================================================
# Object build statements for SHARED_LIBRARY target sqlite3_flutter_libs_plugin


#############################################
# Order-only phony target for sqlite3_flutter_libs_plugin

build cmake_object_order_depends_target_sqlite3_flutter_libs_plugin: phony || flutter/flutter_assemble

build plugins/sqlite3_flutter_libs/CMakeFiles/sqlite3_flutter_libs_plugin.dir/sqlite3_flutter_libs_plugin.cc.o: CXX_COMPILER__sqlite3_flutter_libs_plugin /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/sqlite3_flutter_libs/linux/sqlite3_flutter_libs_plugin.cc || cmake_object_order_depends_target_sqlite3_flutter_libs_plugin
  DEFINES = -DFLUTTER_PLUGIN_IMPL -DSQLITE_DEFAULT_MEMSTATUS=0 -DSQLITE_DQS=0 -DSQLITE_ENABLE_DBSTAT_VTAB -DSQLITE_ENABLE_FTS5 -DSQLITE_ENABLE_MATH_FUNCTIONS -DSQLITE_ENABLE_RTREE -DSQLITE_HAVE_ISNAN -DSQLITE_HAVE_LOCALTIME_R -DSQLITE_HAVE_LOCALTIME_S -DSQLITE_HAVE_MALLOC_USABLE_SIZE -DSQLITE_HAVE_STRCHRNUL -DSQLITE_MAX_EXPR_DEPTH=0 -DSQLITE_OMIT_AUTHORIZATION -DSQLITE_OMIT_DECLTYPE -DSQLITE_OMIT_DEPRECATED -DSQLITE_OMIT_PROGRESS_CALLBACK -DSQLITE_OMIT_SHARED_CACHE -DSQLITE_OMIT_TCL_VARIABLE -DSQLITE_OMIT_TRACE -DSQLITE_STRICT_SUBTYPE=1 -DSQLITE_TEMP_STORE=2 -DSQLITE_UNTESTABLE -DSQLITE_USE_ALLOCA -Dsqlite3_flutter_libs_plugin_EXPORTS
  DEP_FILE = plugins/sqlite3_flutter_libs/CMakeFiles/sqlite3_flutter_libs_plugin.dir/sqlite3_flutter_libs_plugin.cc.o.d
  FLAGS = -g -fPIC -fvisibility=hidden   -pthread
  INCLUDES = -I/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral -isystem /snap/flutter/current/usr/include/gtk-3.0 -isystem /snap/flutter/current/usr/include/at-spi2-atk/2.0 -isystem /snap/flutter/current/usr/include/at-spi-2.0 -isystem /snap/flutter/current/usr/include/dbus-1.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/dbus-1.0/include -isystem /snap/flutter/current/usr/include/gio-unix-2.0 -isystem /snap/flutter/current/usr/include/cairo -isystem /snap/flutter/current/usr/include/pango-1.0 -isystem /snap/flutter/current/usr/include/fribidi -isystem /snap/flutter/current/usr/include/harfbuzz -isystem /snap/flutter/current/usr/include/atk-1.0 -isystem /snap/flutter/current/usr/include/pixman-1 -isystem /snap/flutter/current/usr/include/uuid -isystem /snap/flutter/current/usr/include/freetype2 -isystem /snap/flutter/current/usr/include/libpng16 -isystem /snap/flutter/current/usr/include/gdk-pixbuf-2.0 -isystem /snap/flutter/current/usr/include/libmount -isystem /snap/flutter/current/usr/include/blkid -isystem /snap/flutter/current/usr/include/glib-2.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
  OBJECT_DIR = plugins/sqlite3_flutter_libs/CMakeFiles/sqlite3_flutter_libs_plugin.dir
  OBJECT_FILE_DIR = plugins/sqlite3_flutter_libs/CMakeFiles/sqlite3_flutter_libs_plugin.dir

build plugins/sqlite3_flutter_libs/CMakeFiles/sqlite3_flutter_libs_plugin.dir/__/__/_deps/sqlite3-src/sqlite3.c.o: C_COMPILER__sqlite3_flutter_libs_plugin _deps/sqlite3-src/sqlite3.c || cmake_object_order_depends_target_sqlite3_flutter_libs_plugin
  DEFINES = -DFLUTTER_PLUGIN_IMPL -DSQLITE_DEFAULT_MEMSTATUS=0 -DSQLITE_DQS=0 -DSQLITE_ENABLE_DBSTAT_VTAB -DSQLITE_ENABLE_FTS5 -DSQLITE_ENABLE_MATH_FUNCTIONS -DSQLITE_ENABLE_RTREE -DSQLITE_HAVE_ISNAN -DSQLITE_HAVE_LOCALTIME_R -DSQLITE_HAVE_LOCALTIME_S -DSQLITE_HAVE_MALLOC_USABLE_SIZE -DSQLITE_HAVE_STRCHRNUL -DSQLITE_MAX_EXPR_DEPTH=0 -DSQLITE_OMIT_AUTHORIZATION -DSQLITE_OMIT_DECLTYPE -DSQLITE_OMIT_DEPRECATED -DSQLITE_OMIT_PROGRESS_CALLBACK -DSQLITE_OMIT_SHARED_CACHE -DSQLITE_OMIT_TCL_VARIABLE -DSQLITE_OMIT_TRACE -DSQLITE_STRICT_SUBTYPE=1 -DSQLITE_TEMP_STORE=2 -DSQLITE_UNTESTABLE -DSQLITE_USE_ALLOCA -Dsqlite3_flutter_libs_plugin_EXPORTS
  DEP_FILE = plugins/sqlite3_flutter_libs/CMakeFiles/sqlite3_flutter_libs_plugin.dir/__/__/_deps/sqlite3-src/sqlite3.c.o.d
  FLAGS = -g -fPIC   -pthread
  INCLUDES = -I/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral -isystem /snap/flutter/current/usr/include/gtk-3.0 -isystem /snap/flutter/current/usr/include/at-spi2-atk/2.0 -isystem /snap/flutter/current/usr/include/at-spi-2.0 -isystem /snap/flutter/current/usr/include/dbus-1.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/dbus-1.0/include -isystem /snap/flutter/current/usr/include/gio-unix-2.0 -isystem /snap/flutter/current/usr/include/cairo -isystem /snap/flutter/current/usr/include/pango-1.0 -isystem /snap/flutter/current/usr/include/fribidi -isystem /snap/flutter/current/usr/include/harfbuzz -isystem /snap/flutter/current/usr/include/atk-1.0 -isystem /snap/flutter/current/usr/include/pixman-1 -isystem /snap/flutter/current/usr/include/uuid -isystem /snap/flutter/current/usr/include/freetype2 -isystem /snap/flutter/current/usr/include/libpng16 -isystem /snap/flutter/current/usr/include/gdk-pixbuf-2.0 -isystem /snap/flutter/current/usr/include/libmount -isystem /snap/flutter/current/usr/include/blkid -isystem /snap/flutter/current/usr/include/glib-2.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
  OBJECT_DIR = plugins/sqlite3_flutter_libs/CMakeFiles/sqlite3_flutter_libs_plugin.dir
  OBJECT_FILE_DIR = plugins/sqlite3_flutter_libs/CMakeFiles/sqlite3_flutter_libs_plugin.dir/__/__/_deps/sqlite3-src


# =============================================================================
# Link build statements for SHARED_LIBRARY target sqlite3_flutter_libs_plugin


#############################################
# Link the shared library plugins/sqlite3_flutter_libs/libsqlite3_flutter_libs_plugin.so

build plugins/sqlite3_flutter_libs/libsqlite3_flutter_libs_plugin.so: CXX_SHARED_LIBRARY_LINKER__sqlite3_flutter_libs_plugin plugins/sqlite3_flutter_libs/CMakeFiles/sqlite3_flutter_libs_plugin.dir/sqlite3_flutter_libs_plugin.cc.o plugins/sqlite3_flutter_libs/CMakeFiles/sqlite3_flutter_libs_plugin.dir/__/__/_deps/sqlite3-src/sqlite3.c.o | /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/libflutter_linux_gtk.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgtk-3.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk-3.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpango-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libharfbuzz.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libatk-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo-gobject.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgio-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgobject-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libglib-2.0.so || flutter/flutter_assemble
  LANGUAGE_COMPILE_FLAGS = -g
  LINK_FLAGS = -B/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -B/snap/flutter/current/usr/lib/x86_64-linux-gnu -B/snap/flutter/current/lib/x86_64-linux-gnu -B/snap/flutter/current/usr/lib/ -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib/ -lblkid -lgcrypt -llzma -llz4 -lgpg-error -luuid -lpthread -ldl -lepoxy -lfontconfig
  LINK_LIBRARIES = -Wl,-rpath,/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral  /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/libflutter_linux_gtk.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgtk-3.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk-3.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpango-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libharfbuzz.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libatk-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo-gobject.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgio-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgobject-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libglib-2.0.so
  OBJECT_DIR = plugins/sqlite3_flutter_libs/CMakeFiles/sqlite3_flutter_libs_plugin.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libsqlite3_flutter_libs_plugin.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = plugins/sqlite3_flutter_libs/libsqlite3_flutter_libs_plugin.so
  TARGET_PDB = sqlite3_flutter_libs_plugin.so.dbg

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/generated_plugins.cmake
# =============================================================================


#############################################
# Utility command for install/strip

build plugins/window_manager/CMakeFiles/install/strip.util: CUSTOM_COMMAND plugins/window_manager/all
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/window_manager && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build plugins/window_manager/install/strip: phony plugins/window_manager/CMakeFiles/install/strip.util


#############################################
# Utility command for install/local

build plugins/window_manager/CMakeFiles/install/local.util: CUSTOM_COMMAND plugins/window_manager/all
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/window_manager && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build plugins/window_manager/install/local: phony plugins/window_manager/CMakeFiles/install/local.util


#############################################
# Utility command for install

build plugins/window_manager/CMakeFiles/install.util: CUSTOM_COMMAND plugins/window_manager/all
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/window_manager && /snap/flutter/149/usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build plugins/window_manager/install: phony plugins/window_manager/CMakeFiles/install.util


#############################################
# Utility command for list_install_components

build plugins/window_manager/list_install_components: phony


#############################################
# Utility command for rebuild_cache

build plugins/window_manager/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/window_manager && /snap/flutter/149/usr/bin/cmake -S/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux -B/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build plugins/window_manager/rebuild_cache: phony plugins/window_manager/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for edit_cache

build plugins/window_manager/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/window_manager && /snap/flutter/149/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build plugins/window_manager/edit_cache: phony plugins/window_manager/CMakeFiles/edit_cache.util

# =============================================================================
# Object build statements for SHARED_LIBRARY target window_manager_plugin


#############################################
# Order-only phony target for window_manager_plugin

build cmake_object_order_depends_target_window_manager_plugin: phony || flutter/flutter_assemble

build plugins/window_manager/CMakeFiles/window_manager_plugin.dir/window_manager_plugin.cc.o: CXX_COMPILER__window_manager_plugin /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/window_manager/linux/window_manager_plugin.cc || cmake_object_order_depends_target_window_manager_plugin
  DEFINES = -DFLUTTER_PLUGIN_IMPL -Dwindow_manager_plugin_EXPORTS
  DEP_FILE = plugins/window_manager/CMakeFiles/window_manager_plugin.dir/window_manager_plugin.cc.o.d
  FLAGS = -g -fPIC -fvisibility=hidden   -Wall -Werror -pthread
  INCLUDES = -I/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral -isystem /snap/flutter/current/usr/include/gtk-3.0 -isystem /snap/flutter/current/usr/include/at-spi2-atk/2.0 -isystem /snap/flutter/current/usr/include/at-spi-2.0 -isystem /snap/flutter/current/usr/include/dbus-1.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/dbus-1.0/include -isystem /snap/flutter/current/usr/include/gio-unix-2.0 -isystem /snap/flutter/current/usr/include/cairo -isystem /snap/flutter/current/usr/include/pango-1.0 -isystem /snap/flutter/current/usr/include/fribidi -isystem /snap/flutter/current/usr/include/harfbuzz -isystem /snap/flutter/current/usr/include/atk-1.0 -isystem /snap/flutter/current/usr/include/pixman-1 -isystem /snap/flutter/current/usr/include/uuid -isystem /snap/flutter/current/usr/include/freetype2 -isystem /snap/flutter/current/usr/include/libpng16 -isystem /snap/flutter/current/usr/include/gdk-pixbuf-2.0 -isystem /snap/flutter/current/usr/include/libmount -isystem /snap/flutter/current/usr/include/blkid -isystem /snap/flutter/current/usr/include/glib-2.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
  OBJECT_DIR = plugins/window_manager/CMakeFiles/window_manager_plugin.dir
  OBJECT_FILE_DIR = plugins/window_manager/CMakeFiles/window_manager_plugin.dir


# =============================================================================
# Link build statements for SHARED_LIBRARY target window_manager_plugin


#############################################
# Link the shared library plugins/window_manager/libwindow_manager_plugin.so

build plugins/window_manager/libwindow_manager_plugin.so: CXX_SHARED_LIBRARY_LINKER__window_manager_plugin plugins/window_manager/CMakeFiles/window_manager_plugin.dir/window_manager_plugin.cc.o | /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/libflutter_linux_gtk.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgtk-3.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk-3.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpango-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libharfbuzz.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libatk-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo-gobject.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgio-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgobject-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libglib-2.0.so || flutter/flutter_assemble
  LANGUAGE_COMPILE_FLAGS = -g
  LINK_FLAGS = -B/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -B/snap/flutter/current/usr/lib/x86_64-linux-gnu -B/snap/flutter/current/lib/x86_64-linux-gnu -B/snap/flutter/current/usr/lib/ -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib/ -lblkid -lgcrypt -llzma -llz4 -lgpg-error -luuid -lpthread -ldl -lepoxy -lfontconfig
  LINK_LIBRARIES = -Wl,-rpath,/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral  /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/libflutter_linux_gtk.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgtk-3.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk-3.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpango-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libharfbuzz.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libatk-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo-gobject.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgio-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgobject-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libglib-2.0.so
  OBJECT_DIR = plugins/window_manager/CMakeFiles/window_manager_plugin.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libwindow_manager_plugin.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = plugins/window_manager/libwindow_manager_plugin.so
  TARGET_PDB = window_manager_plugin.so.dbg

# =============================================================================
# Target aliases.

build auto_parts_erp: phony intermediates_do_not_run/auto_parts_erp

build file_selector_linux_plugin: phony plugins/file_selector_linux/libfile_selector_linux_plugin.so

build flutter_assemble: phony flutter/flutter_assemble

build libfile_selector_linux_plugin.so: phony plugins/file_selector_linux/libfile_selector_linux_plugin.so

build libprinting_plugin.so: phony plugins/printing/libprinting_plugin.so

build libscreen_retriever_plugin.so: phony plugins/screen_retriever/libscreen_retriever_plugin.so

build libsqlite3_flutter_libs_plugin.so: phony plugins/sqlite3_flutter_libs/libsqlite3_flutter_libs_plugin.so

build libwindow_manager_plugin.so: phony plugins/window_manager/libwindow_manager_plugin.so

build printing_plugin: phony plugins/printing/libprinting_plugin.so

build screen_retriever_plugin: phony plugins/screen_retriever/libscreen_retriever_plugin.so

build sqlite3_flutter_libs_plugin: phony plugins/sqlite3_flutter_libs/libsqlite3_flutter_libs_plugin.so

build window_manager_plugin: phony plugins/window_manager/libwindow_manager_plugin.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug

build all: phony flutter/all runner/all plugins/file_selector_linux/all plugins/printing/all plugins/screen_retriever/all plugins/sqlite3_flutter_libs/all plugins/window_manager/all

# =============================================================================

#############################################
# Folder: /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/flutter

build flutter/all: phony

# =============================================================================

#############################################
# Folder: /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/file_selector_linux

build plugins/file_selector_linux/all: phony plugins/file_selector_linux/libfile_selector_linux_plugin.so

# =============================================================================

#############################################
# Folder: /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/printing

build plugins/printing/all: phony plugins/printing/libprinting_plugin.so

# =============================================================================

#############################################
# Folder: /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/screen_retriever

build plugins/screen_retriever/all: phony plugins/screen_retriever/libscreen_retriever_plugin.so

# =============================================================================

#############################################
# Folder: /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/sqlite3_flutter_libs

build plugins/sqlite3_flutter_libs/all: phony plugins/sqlite3_flutter_libs/libsqlite3_flutter_libs_plugin.so

# =============================================================================

#############################################
# Folder: /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/plugins/window_manager

build plugins/window_manager/all: phony plugins/window_manager/libwindow_manager_plugin.so

# =============================================================================

#############################################
# Folder: /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/runner

build runner/all: phony intermediates_do_not_run/auto_parts_erp

# =============================================================================
# Built-in targets


#############################################
# Make the all target the default.

default all

#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/CMakeLists.txt /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/CMakeLists.txt /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/file_selector_linux/linux/CMakeLists.txt /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/printing/linux/CMakeLists.txt /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/printing/windows/DownloadProject.CMakeLists.cmake.in /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/printing/windows/DownloadProject.cmake /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/screen_retriever/linux/CMakeLists.txt /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/sqlite3_flutter_libs/linux/CMakeLists.txt /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/window_manager/linux/CMakeLists.txt /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/generated_config.cmake /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/generated_plugins.cmake /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/runner/CMakeLists.txt /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCInformation.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCXXInformation.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCommonLanguageInclude.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeGenericSystem.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeInitializeConfigs.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeLanguageInformation.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeParseArguments.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeSystemSpecificInformation.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeSystemSpecificInitialize.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/CMakeCommonCompilerMacros.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Clang-C.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Clang-CXX.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Clang.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/GNU.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/FetchContent.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/FetchContent/CMakeLists.cmake.in /snap/flutter/149/usr/share/cmake-3.16/Modules/FindPackageHandleStandardArgs.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/FindPackageMessage.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/FindPkgConfig.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Internal/CMakeCheckCompilerFlag.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux-Clang-C.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux-Clang-CXX.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux-GNU-C.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux-GNU-CXX.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux-GNU.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.16.3/CMakeCCompiler.cmake CMakeFiles/3.16.3/CMakeCXXCompiler.cmake CMakeFiles/3.16.3/CMakeSystem.cmake pdfium-src/PDFiumConfig.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/CMakeLists.txt /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/CMakeLists.txt /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/file_selector_linux/linux/CMakeLists.txt /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/printing/linux/CMakeLists.txt /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/printing/windows/DownloadProject.CMakeLists.cmake.in /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/printing/windows/DownloadProject.cmake /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/screen_retriever/linux/CMakeLists.txt /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/sqlite3_flutter_libs/linux/CMakeLists.txt /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/.plugin_symlinks/window_manager/linux/CMakeLists.txt /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/ephemeral/generated_config.cmake /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/flutter/generated_plugins.cmake /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux/runner/CMakeLists.txt /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCInformation.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCXXInformation.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCommonLanguageInclude.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeGenericSystem.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeInitializeConfigs.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeLanguageInformation.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeParseArguments.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeSystemSpecificInformation.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeSystemSpecificInitialize.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/CMakeCommonCompilerMacros.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Clang-C.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Clang-CXX.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Clang.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/GNU.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/FetchContent.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/FetchContent/CMakeLists.cmake.in /snap/flutter/149/usr/share/cmake-3.16/Modules/FindPackageHandleStandardArgs.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/FindPackageMessage.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/FindPkgConfig.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Internal/CMakeCheckCompilerFlag.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux-Clang-C.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux-Clang-CXX.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux-GNU-C.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux-GNU-CXX.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux-GNU.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.16.3/CMakeCCompiler.cmake CMakeFiles/3.16.3/CMakeCXXCompiler.cmake CMakeFiles/3.16.3/CMakeSystem.cmake pdfium-src/PDFiumConfig.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP

