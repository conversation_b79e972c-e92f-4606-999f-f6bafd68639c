The system is: Linux - 6.11.0-28-generic - x86_64
Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: /snap/flutter/current/usr/bin/clang++ 
Build flags: 
Id flags: -c 

The output was:
0


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.o"

The CXX compiler identification is Clang, found in "/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/CMakeFiles/3.16.3/CompilerIdCXX/CMakeCXXCompilerId.o"

Determining if the CXX compiler works passed with the following output:
Change Dir: /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/CMakeFiles/CMakeTmp

Run Build Command(s):/snap/flutter/current/usr/bin/ninja cmTC_8e19d && [1/2] Building CXX object CMakeFiles/cmTC_8e19d.dir/testCXXCompiler.cxx.o
[2/2] Linking CXX executable cmTC_8e19d



Detecting CXX compiler ABI info compiled with the following output:
Change Dir: /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/CMakeFiles/CMakeTmp

Run Build Command(s):/snap/flutter/current/usr/bin/ninja cmTC_27d7b && [1/2] Building CXX object CMakeFiles/cmTC_27d7b.dir/CMakeCXXCompilerABI.cpp.o
clang version 10.0.0-4ubuntu1 
Target: x86_64-pc-linux-gnu
Thread model: posix
InstalledDir: /snap/flutter/current/usr/bin
Found candidate GCC installation: /snap/flutter/current/usr/bin/../lib/gcc/x86_64-linux-gnu/10
Found candidate GCC installation: /snap/flutter/current/usr/bin/../lib/gcc/x86_64-linux-gnu/9
Found candidate GCC installation: /usr/lib/gcc/x86_64-linux-gnu/13
Selected GCC installation: /usr/lib/gcc/x86_64-linux-gnu/13
Candidate multilib: .;@m64
Selected multilib: .;@m64
 (in-process)
 "/snap/flutter/149/usr/lib/llvm-10/bin/clang" -cc1 -triple x86_64-pc-linux-gnu -emit-obj -mrelax-all -disable-free -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model static -mthread-model posix -mframe-pointer=all -fmath-errno -fno-rounding-math -masm-verbose -mconstructor-aliases -munwind-tables -target-cpu x86-64 -dwarf-column-info -fno-split-dwarf-inlining -debugger-tuning=gdb -v -resource-dir /snap/flutter/149/usr/lib/llvm-10/lib/clang/10.0.0 -cxx-isystem /snap/flutter/current/usr/include/x86_64-linux-gnu/c++/9 -cxx-isystem /snap/flutter/current/usr/include/c++/9 -cxx-isystem /snap/flutter/current/usr/include -cxx-isystem /snap/flutter/current/usr/include/x86_64-linux-gnu -cxx-isystem /snap/flutter/current/usr/include/c++/9 -internal-isystem /usr/lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13 -internal-isystem /usr/lib/gcc/x86_64-linux-gnu/13/../../../../include/x86_64-linux-gnu/c++/13 -internal-isystem /usr/lib/gcc/x86_64-linux-gnu/13/../../../../include/x86_64-linux-gnu/c++/13 -internal-isystem /usr/lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13/backward -internal-isystem /usr/local/include -internal-isystem /snap/flutter/149/usr/lib/llvm-10/lib/clang/10.0.0/include -internal-externc-isystem /usr/include/x86_64-linux-gnu -internal-externc-isystem /include -internal-externc-isystem /usr/include -fdeprecated-macro -fdebug-compilation-dir /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/CMakeFiles/CMakeTmp -ferror-limit 19 -fmessage-length 0 -fgnuc-version=4.2.1 -fobjc-runtime=gcc -fcxx-exceptions -fexceptions -fdiagnostics-show-option -faddrsig -o CMakeFiles/cmTC_27d7b.dir/CMakeCXXCompilerABI.cpp.o -x c++ /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCXXCompilerABI.cpp
clang -cc1 version 10.0.0 based upon LLVM 10.0.0 default target x86_64-pc-linux-gnu
ignoring nonexistent directory "/include"
ignoring duplicate directory "/snap/flutter/current/usr/include/c++/9"
ignoring duplicate directory "/usr/lib/gcc/x86_64-linux-gnu/13/../../../../include/x86_64-linux-gnu/c++/13"
#include "..." search starts here:
#include <...> search starts here:
 /snap/flutter/current/usr/include/x86_64-linux-gnu/c++/9
 /snap/flutter/current/usr/include/c++/9
 /snap/flutter/current/usr/include
 /snap/flutter/current/usr/include/x86_64-linux-gnu
 /usr/lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13
 /usr/lib/gcc/x86_64-linux-gnu/13/../../../../include/x86_64-linux-gnu/c++/13
 /usr/lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13/backward
 /usr/local/include
 /snap/flutter/149/usr/lib/llvm-10/lib/clang/10.0.0/include
 /usr/include/x86_64-linux-gnu
 /usr/include
End of search list.
[2/2] Linking CXX executable cmTC_27d7b
clang version 10.0.0-4ubuntu1 
Target: x86_64-pc-linux-gnu
Thread model: posix
InstalledDir: /snap/flutter/current/usr/bin
Found candidate GCC installation: /snap/flutter/current/usr/bin/../lib/gcc/x86_64-linux-gnu/10
Found candidate GCC installation: /snap/flutter/current/usr/bin/../lib/gcc/x86_64-linux-gnu/9
Found candidate GCC installation: /usr/lib/gcc/x86_64-linux-gnu/13
Selected GCC installation: /usr/lib/gcc/x86_64-linux-gnu/13
Candidate multilib: .;@m64
Selected multilib: .;@m64
 "/snap/flutter/current/usr/bin/ld" --build-id --eh-frame-hdr -m elf_x86_64 -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_27d7b /snap/flutter/current/usr/lib/x86_64-linux-gnu/crt1.o /snap/flutter/current/usr/lib/x86_64-linux-gnu/crti.o /snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9/crtbegin.o -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib/ -L/usr/lib/gcc/x86_64-linux-gnu/13 -L/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/13/../../../../lib64 -L/lib/x86_64-linux-gnu -L/lib/../lib64 -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib64 -L/usr/lib/x86_64-linux-gnu/../../lib64 -L/usr/lib/gcc/x86_64-linux-gnu/13/../../.. -L/snap/flutter/149/usr/lib/llvm-10/bin/../lib -L/lib -L/usr/lib -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib -lblkid -lgcrypt -llzma -llz4 -lgpg-error -luuid -lpthread -ldl -lepoxy -lfontconfig CMakeFiles/cmTC_27d7b.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9/crtend.o /snap/flutter/current/usr/lib/x86_64-linux-gnu/crtn.o



Parsed CXX implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [/snap/flutter/current/usr/include/x86_64-linux-gnu/c++/9]
    add: [/snap/flutter/current/usr/include/c++/9]
    add: [/snap/flutter/current/usr/include]
    add: [/snap/flutter/current/usr/include/x86_64-linux-gnu]
    add: [/usr/lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13]
    add: [/usr/lib/gcc/x86_64-linux-gnu/13/../../../../include/x86_64-linux-gnu/c++/13]
    add: [/usr/lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13/backward]
    add: [/usr/local/include]
    add: [/snap/flutter/149/usr/lib/llvm-10/lib/clang/10.0.0/include]
    add: [/usr/include/x86_64-linux-gnu]
    add: [/usr/include]
  end of search list found
  collapse include dir [/snap/flutter/current/usr/include/x86_64-linux-gnu/c++/9] ==> [/snap/flutter/current/usr/include/x86_64-linux-gnu/c++/9]
  collapse include dir [/snap/flutter/current/usr/include/c++/9] ==> [/snap/flutter/current/usr/include/c++/9]
  collapse include dir [/snap/flutter/current/usr/include] ==> [/snap/flutter/current/usr/include]
  collapse include dir [/snap/flutter/current/usr/include/x86_64-linux-gnu] ==> [/snap/flutter/current/usr/include/x86_64-linux-gnu]
  collapse include dir [/usr/lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13] ==> [/usr/include/c++/13]
  collapse include dir [/usr/lib/gcc/x86_64-linux-gnu/13/../../../../include/x86_64-linux-gnu/c++/13] ==> [/usr/include/x86_64-linux-gnu/c++/13]
  collapse include dir [/usr/lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13/backward] ==> [/usr/include/c++/13/backward]
  collapse include dir [/usr/local/include] ==> [/usr/local/include]
  collapse include dir [/snap/flutter/149/usr/lib/llvm-10/lib/clang/10.0.0/include] ==> [/snap/flutter/149/usr/lib/llvm-10/lib/clang/10.0.0/include]
  collapse include dir [/usr/include/x86_64-linux-gnu] ==> [/usr/include/x86_64-linux-gnu]
  collapse include dir [/usr/include] ==> [/usr/include]
  implicit include dirs: [/snap/flutter/current/usr/include/x86_64-linux-gnu/c++/9;/snap/flutter/current/usr/include/c++/9;/snap/flutter/current/usr/include;/snap/flutter/current/usr/include/x86_64-linux-gnu;/usr/include/c++/13;/usr/include/x86_64-linux-gnu/c++/13;/usr/include/c++/13/backward;/usr/local/include;/snap/flutter/149/usr/lib/llvm-10/lib/clang/10.0.0/include;/usr/include/x86_64-linux-gnu;/usr/include]


Parsed CXX implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):/snap/flutter/current/usr/bin/ninja cmTC_27d7b && [1/2] Building CXX object CMakeFiles/cmTC_27d7b.dir/CMakeCXXCompilerABI.cpp.o]
  ignore line: [clang version 10.0.0-4ubuntu1 ]
  ignore line: [Target: x86_64-pc-linux-gnu]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: /snap/flutter/current/usr/bin]
  ignore line: [Found candidate GCC installation: /snap/flutter/current/usr/bin/../lib/gcc/x86_64-linux-gnu/10]
  ignore line: [Found candidate GCC installation: /snap/flutter/current/usr/bin/../lib/gcc/x86_64-linux-gnu/9]
  ignore line: [Found candidate GCC installation: /usr/lib/gcc/x86_64-linux-gnu/13]
  ignore line: [Selected GCC installation: /usr/lib/gcc/x86_64-linux-gnu/13]
  ignore line: [Candidate multilib: .]
  ignore line: [@m64]
  ignore line: [Selected multilib: .]
  ignore line: [@m64]
  ignore line: [ (in-process)]
  ignore line: [ "/snap/flutter/149/usr/lib/llvm-10/bin/clang" -cc1 -triple x86_64-pc-linux-gnu -emit-obj -mrelax-all -disable-free -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model static -mthread-model posix -mframe-pointer=all -fmath-errno -fno-rounding-math -masm-verbose -mconstructor-aliases -munwind-tables -target-cpu x86-64 -dwarf-column-info -fno-split-dwarf-inlining -debugger-tuning=gdb -v -resource-dir /snap/flutter/149/usr/lib/llvm-10/lib/clang/10.0.0 -cxx-isystem /snap/flutter/current/usr/include/x86_64-linux-gnu/c++/9 -cxx-isystem /snap/flutter/current/usr/include/c++/9 -cxx-isystem /snap/flutter/current/usr/include -cxx-isystem /snap/flutter/current/usr/include/x86_64-linux-gnu -cxx-isystem /snap/flutter/current/usr/include/c++/9 -internal-isystem /usr/lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13 -internal-isystem /usr/lib/gcc/x86_64-linux-gnu/13/../../../../include/x86_64-linux-gnu/c++/13 -internal-isystem /usr/lib/gcc/x86_64-linux-gnu/13/../../../../include/x86_64-linux-gnu/c++/13 -internal-isystem /usr/lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13/backward -internal-isystem /usr/local/include -internal-isystem /snap/flutter/149/usr/lib/llvm-10/lib/clang/10.0.0/include -internal-externc-isystem /usr/include/x86_64-linux-gnu -internal-externc-isystem /include -internal-externc-isystem /usr/include -fdeprecated-macro -fdebug-compilation-dir /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/CMakeFiles/CMakeTmp -ferror-limit 19 -fmessage-length 0 -fgnuc-version=4.2.1 -fobjc-runtime=gcc -fcxx-exceptions -fexceptions -fdiagnostics-show-option -faddrsig -o CMakeFiles/cmTC_27d7b.dir/CMakeCXXCompilerABI.cpp.o -x c++ /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCXXCompilerABI.cpp]
  ignore line: [clang -cc1 version 10.0.0 based upon LLVM 10.0.0 default target x86_64-pc-linux-gnu]
  ignore line: [ignoring nonexistent directory "/include"]
  ignore line: [ignoring duplicate directory "/snap/flutter/current/usr/include/c++/9"]
  ignore line: [ignoring duplicate directory "/usr/lib/gcc/x86_64-linux-gnu/13/../../../../include/x86_64-linux-gnu/c++/13"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ /snap/flutter/current/usr/include/x86_64-linux-gnu/c++/9]
  ignore line: [ /snap/flutter/current/usr/include/c++/9]
  ignore line: [ /snap/flutter/current/usr/include]
  ignore line: [ /snap/flutter/current/usr/include/x86_64-linux-gnu]
  ignore line: [ /usr/lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13]
  ignore line: [ /usr/lib/gcc/x86_64-linux-gnu/13/../../../../include/x86_64-linux-gnu/c++/13]
  ignore line: [ /usr/lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13/backward]
  ignore line: [ /usr/local/include]
  ignore line: [ /snap/flutter/149/usr/lib/llvm-10/lib/clang/10.0.0/include]
  ignore line: [ /usr/include/x86_64-linux-gnu]
  ignore line: [ /usr/include]
  ignore line: [End of search list.]
  ignore line: [[2/2] Linking CXX executable cmTC_27d7b]
  ignore line: [clang version 10.0.0-4ubuntu1 ]
  ignore line: [Target: x86_64-pc-linux-gnu]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: /snap/flutter/current/usr/bin]
  ignore line: [Found candidate GCC installation: /snap/flutter/current/usr/bin/../lib/gcc/x86_64-linux-gnu/10]
  ignore line: [Found candidate GCC installation: /snap/flutter/current/usr/bin/../lib/gcc/x86_64-linux-gnu/9]
  ignore line: [Found candidate GCC installation: /usr/lib/gcc/x86_64-linux-gnu/13]
  ignore line: [Selected GCC installation: /usr/lib/gcc/x86_64-linux-gnu/13]
  ignore line: [Candidate multilib: .]
  ignore line: [@m64]
  ignore line: [Selected multilib: .]
  ignore line: [@m64]
  link line: [ "/snap/flutter/current/usr/bin/ld" --build-id --eh-frame-hdr -m elf_x86_64 -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_27d7b /snap/flutter/current/usr/lib/x86_64-linux-gnu/crt1.o /snap/flutter/current/usr/lib/x86_64-linux-gnu/crti.o /snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9/crtbegin.o -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib/ -L/usr/lib/gcc/x86_64-linux-gnu/13 -L/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/13/../../../../lib64 -L/lib/x86_64-linux-gnu -L/lib/../lib64 -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib64 -L/usr/lib/x86_64-linux-gnu/../../lib64 -L/usr/lib/gcc/x86_64-linux-gnu/13/../../.. -L/snap/flutter/149/usr/lib/llvm-10/bin/../lib -L/lib -L/usr/lib -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib -lblkid -lgcrypt -llzma -llz4 -lgpg-error -luuid -lpthread -ldl -lepoxy -lfontconfig CMakeFiles/cmTC_27d7b.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9/crtend.o /snap/flutter/current/usr/lib/x86_64-linux-gnu/crtn.o]
    arg [/snap/flutter/current/usr/bin/ld] ==> ignore
    arg [--build-id] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-m] ==> ignore
    arg [elf_x86_64] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/lib64/ld-linux-x86-64.so.2] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_27d7b] ==> ignore
    arg [/snap/flutter/current/usr/lib/x86_64-linux-gnu/crt1.o] ==> ignore
    arg [/snap/flutter/current/usr/lib/x86_64-linux-gnu/crti.o] ==> ignore
    arg [/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9/crtbegin.o] ==> ignore
    arg [-L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9] ==> dir [/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9]
    arg [-L/snap/flutter/current/usr/lib/x86_64-linux-gnu] ==> dir [/snap/flutter/current/usr/lib/x86_64-linux-gnu]
    arg [-L/snap/flutter/current/lib/x86_64-linux-gnu] ==> dir [/snap/flutter/current/lib/x86_64-linux-gnu]
    arg [-L/snap/flutter/current/usr/lib/] ==> dir [/snap/flutter/current/usr/lib/]
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/13] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/13]
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu]
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/13/../../../../lib64] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/13/../../../../lib64]
    arg [-L/lib/x86_64-linux-gnu] ==> dir [/lib/x86_64-linux-gnu]
    arg [-L/lib/../lib64] ==> dir [/lib/../lib64]
    arg [-L/usr/lib/x86_64-linux-gnu] ==> dir [/usr/lib/x86_64-linux-gnu]
    arg [-L/usr/lib/../lib64] ==> dir [/usr/lib/../lib64]
    arg [-L/usr/lib/x86_64-linux-gnu/../../lib64] ==> dir [/usr/lib/x86_64-linux-gnu/../../lib64]
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/13/../../..] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/13/../../..]
    arg [-L/snap/flutter/149/usr/lib/llvm-10/bin/../lib] ==> dir [/snap/flutter/149/usr/lib/llvm-10/bin/../lib]
    arg [-L/lib] ==> dir [/lib]
    arg [-L/usr/lib] ==> dir [/usr/lib]
    arg [-L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9] ==> dir [/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9]
    arg [-L/snap/flutter/current/usr/lib/x86_64-linux-gnu] ==> dir [/snap/flutter/current/usr/lib/x86_64-linux-gnu]
    arg [-L/snap/flutter/current/lib/x86_64-linux-gnu] ==> dir [/snap/flutter/current/lib/x86_64-linux-gnu]
    arg [-L/snap/flutter/current/usr/lib] ==> dir [/snap/flutter/current/usr/lib]
    arg [-lblkid] ==> lib [blkid]
    arg [-lgcrypt] ==> lib [gcrypt]
    arg [-llzma] ==> lib [lzma]
    arg [-llz4] ==> lib [lz4]
    arg [-lgpg-error] ==> lib [gpg-error]
    arg [-luuid] ==> lib [uuid]
    arg [-lpthread] ==> lib [pthread]
    arg [-ldl] ==> lib [dl]
    arg [-lepoxy] ==> lib [epoxy]
    arg [-lfontconfig] ==> lib [fontconfig]
    arg [CMakeFiles/cmTC_27d7b.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
    arg [-lstdc++] ==> lib [stdc++]
    arg [-lm] ==> lib [m]
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [-lgcc] ==> lib [gcc]
    arg [-lc] ==> lib [c]
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [-lgcc] ==> lib [gcc]
    arg [/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9/crtend.o] ==> ignore
    arg [/snap/flutter/current/usr/lib/x86_64-linux-gnu/crtn.o] ==> ignore
  collapse library dir [/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9] ==> [/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9]
  collapse library dir [/snap/flutter/current/usr/lib/x86_64-linux-gnu] ==> [/snap/flutter/current/usr/lib/x86_64-linux-gnu]
  collapse library dir [/snap/flutter/current/lib/x86_64-linux-gnu] ==> [/snap/flutter/current/lib/x86_64-linux-gnu]
  collapse library dir [/snap/flutter/current/usr/lib/] ==> [/snap/flutter/current/usr/lib]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/13] ==> [/usr/lib/gcc/x86_64-linux-gnu/13]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/13/../../../../lib64] ==> [/usr/lib64]
  collapse library dir [/lib/x86_64-linux-gnu] ==> [/lib/x86_64-linux-gnu]
  collapse library dir [/lib/../lib64] ==> [/lib64]
  collapse library dir [/usr/lib/x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
  collapse library dir [/usr/lib/../lib64] ==> [/usr/lib64]
  collapse library dir [/usr/lib/x86_64-linux-gnu/../../lib64] ==> [/usr/lib64]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/13/../../..] ==> [/usr/lib]
  collapse library dir [/snap/flutter/149/usr/lib/llvm-10/bin/../lib] ==> [/snap/flutter/149/usr/lib/llvm-10/lib]
  collapse library dir [/lib] ==> [/lib]
  collapse library dir [/usr/lib] ==> [/usr/lib]
  collapse library dir [/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9] ==> [/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9]
  collapse library dir [/snap/flutter/current/usr/lib/x86_64-linux-gnu] ==> [/snap/flutter/current/usr/lib/x86_64-linux-gnu]
  collapse library dir [/snap/flutter/current/lib/x86_64-linux-gnu] ==> [/snap/flutter/current/lib/x86_64-linux-gnu]
  collapse library dir [/snap/flutter/current/usr/lib] ==> [/snap/flutter/current/usr/lib]
  implicit libs: [blkid;gcrypt;lzma;lz4;gpg-error;uuid;pthread;dl;epoxy;fontconfig;stdc++;m;gcc_s;gcc;c;gcc_s;gcc]
  implicit dirs: [/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9;/snap/flutter/current/usr/lib/x86_64-linux-gnu;/snap/flutter/current/lib/x86_64-linux-gnu;/snap/flutter/current/usr/lib;/usr/lib/gcc/x86_64-linux-gnu/13;/usr/lib/x86_64-linux-gnu;/usr/lib64;/lib/x86_64-linux-gnu;/lib64;/usr/lib;/snap/flutter/149/usr/lib/llvm-10/lib;/lib]
  implicit fwks: []


Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: /snap/flutter/current/usr/bin/clang 
Build flags: 
Id flags: -c 

The output was:
0


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.o"

The C compiler identification is Clang, found in "/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/CMakeFiles/3.16.3/CompilerIdC/CMakeCCompilerId.o"

Determining if the C compiler works passed with the following output:
Change Dir: /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/CMakeFiles/CMakeTmp

Run Build Command(s):/snap/flutter/current/usr/bin/ninja cmTC_eb324 && [1/2] Building C object CMakeFiles/cmTC_eb324.dir/testCCompiler.c.o
[2/2] Linking C executable cmTC_eb324



Detecting C compiler ABI info compiled with the following output:
Change Dir: /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/CMakeFiles/CMakeTmp

Run Build Command(s):/snap/flutter/current/usr/bin/ninja cmTC_74c10 && [1/2] Building C object CMakeFiles/cmTC_74c10.dir/CMakeCCompilerABI.c.o
clang version 10.0.0-4ubuntu1 
Target: x86_64-pc-linux-gnu
Thread model: posix
InstalledDir: /snap/flutter/current/usr/bin
Found candidate GCC installation: /snap/flutter/current/usr/bin/../lib/gcc/x86_64-linux-gnu/10
Found candidate GCC installation: /snap/flutter/current/usr/bin/../lib/gcc/x86_64-linux-gnu/9
Found candidate GCC installation: /usr/lib/gcc/x86_64-linux-gnu/13
Selected GCC installation: /usr/lib/gcc/x86_64-linux-gnu/13
Candidate multilib: .;@m64
Selected multilib: .;@m64
 (in-process)
 "/snap/flutter/149/usr/lib/llvm-10/bin/clang" -cc1 -triple x86_64-pc-linux-gnu -emit-obj -mrelax-all -disable-free -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model static -mthread-model posix -mframe-pointer=all -fmath-errno -fno-rounding-math -masm-verbose -mconstructor-aliases -munwind-tables -target-cpu x86-64 -dwarf-column-info -fno-split-dwarf-inlining -debugger-tuning=gdb -v -resource-dir /snap/flutter/149/usr/lib/llvm-10/lib/clang/10.0.0 -cxx-isystem /snap/flutter/current/usr/include/x86_64-linux-gnu/c++/9 -cxx-isystem /snap/flutter/current/usr/include/c++/9 -cxx-isystem /snap/flutter/current/usr/include -cxx-isystem /snap/flutter/current/usr/include/x86_64-linux-gnu -cxx-isystem /snap/flutter/current/usr/include/c++/9 -internal-isystem /usr/local/include -internal-isystem /snap/flutter/149/usr/lib/llvm-10/lib/clang/10.0.0/include -internal-externc-isystem /usr/include/x86_64-linux-gnu -internal-externc-isystem /include -internal-externc-isystem /usr/include -fdebug-compilation-dir /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/CMakeFiles/CMakeTmp -ferror-limit 19 -fmessage-length 0 -fgnuc-version=4.2.1 -fobjc-runtime=gcc -fdiagnostics-show-option -faddrsig -o CMakeFiles/cmTC_74c10.dir/CMakeCCompilerABI.c.o -x c /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCCompilerABI.c
clang -cc1 version 10.0.0 based upon LLVM 10.0.0 default target x86_64-pc-linux-gnu
ignoring nonexistent directory "/include"
#include "..." search starts here:
#include <...> search starts here:
 /usr/local/include
 /snap/flutter/149/usr/lib/llvm-10/lib/clang/10.0.0/include
 /usr/include/x86_64-linux-gnu
 /usr/include
End of search list.
[2/2] Linking C executable cmTC_74c10
clang version 10.0.0-4ubuntu1 
Target: x86_64-pc-linux-gnu
Thread model: posix
InstalledDir: /snap/flutter/current/usr/bin
Found candidate GCC installation: /snap/flutter/current/usr/bin/../lib/gcc/x86_64-linux-gnu/10
Found candidate GCC installation: /snap/flutter/current/usr/bin/../lib/gcc/x86_64-linux-gnu/9
Found candidate GCC installation: /usr/lib/gcc/x86_64-linux-gnu/13
Selected GCC installation: /usr/lib/gcc/x86_64-linux-gnu/13
Candidate multilib: .;@m64
Selected multilib: .;@m64
 "/snap/flutter/current/usr/bin/ld" --build-id --eh-frame-hdr -m elf_x86_64 -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_74c10 /snap/flutter/current/usr/lib/x86_64-linux-gnu/crt1.o /snap/flutter/current/usr/lib/x86_64-linux-gnu/crti.o /snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9/crtbegin.o -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib/ -L/usr/lib/gcc/x86_64-linux-gnu/13 -L/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/13/../../../../lib64 -L/lib/x86_64-linux-gnu -L/lib/../lib64 -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib64 -L/usr/lib/x86_64-linux-gnu/../../lib64 -L/usr/lib/gcc/x86_64-linux-gnu/13/../../.. -L/snap/flutter/149/usr/lib/llvm-10/bin/../lib -L/lib -L/usr/lib -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib -lblkid -lgcrypt -llzma -llz4 -lgpg-error -luuid -lpthread -ldl -lepoxy -lfontconfig CMakeFiles/cmTC_74c10.dir/CMakeCCompilerABI.c.o -lgcc --as-needed -lgcc_s --no-as-needed -lc -lgcc --as-needed -lgcc_s --no-as-needed /snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9/crtend.o /snap/flutter/current/usr/lib/x86_64-linux-gnu/crtn.o



Parsed C implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [/usr/local/include]
    add: [/snap/flutter/149/usr/lib/llvm-10/lib/clang/10.0.0/include]
    add: [/usr/include/x86_64-linux-gnu]
    add: [/usr/include]
  end of search list found
  collapse include dir [/usr/local/include] ==> [/usr/local/include]
  collapse include dir [/snap/flutter/149/usr/lib/llvm-10/lib/clang/10.0.0/include] ==> [/snap/flutter/149/usr/lib/llvm-10/lib/clang/10.0.0/include]
  collapse include dir [/usr/include/x86_64-linux-gnu] ==> [/usr/include/x86_64-linux-gnu]
  collapse include dir [/usr/include] ==> [/usr/include]
  implicit include dirs: [/usr/local/include;/snap/flutter/149/usr/lib/llvm-10/lib/clang/10.0.0/include;/usr/include/x86_64-linux-gnu;/usr/include]


Parsed C implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):/snap/flutter/current/usr/bin/ninja cmTC_74c10 && [1/2] Building C object CMakeFiles/cmTC_74c10.dir/CMakeCCompilerABI.c.o]
  ignore line: [clang version 10.0.0-4ubuntu1 ]
  ignore line: [Target: x86_64-pc-linux-gnu]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: /snap/flutter/current/usr/bin]
  ignore line: [Found candidate GCC installation: /snap/flutter/current/usr/bin/../lib/gcc/x86_64-linux-gnu/10]
  ignore line: [Found candidate GCC installation: /snap/flutter/current/usr/bin/../lib/gcc/x86_64-linux-gnu/9]
  ignore line: [Found candidate GCC installation: /usr/lib/gcc/x86_64-linux-gnu/13]
  ignore line: [Selected GCC installation: /usr/lib/gcc/x86_64-linux-gnu/13]
  ignore line: [Candidate multilib: .]
  ignore line: [@m64]
  ignore line: [Selected multilib: .]
  ignore line: [@m64]
  ignore line: [ (in-process)]
  ignore line: [ "/snap/flutter/149/usr/lib/llvm-10/bin/clang" -cc1 -triple x86_64-pc-linux-gnu -emit-obj -mrelax-all -disable-free -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model static -mthread-model posix -mframe-pointer=all -fmath-errno -fno-rounding-math -masm-verbose -mconstructor-aliases -munwind-tables -target-cpu x86-64 -dwarf-column-info -fno-split-dwarf-inlining -debugger-tuning=gdb -v -resource-dir /snap/flutter/149/usr/lib/llvm-10/lib/clang/10.0.0 -cxx-isystem /snap/flutter/current/usr/include/x86_64-linux-gnu/c++/9 -cxx-isystem /snap/flutter/current/usr/include/c++/9 -cxx-isystem /snap/flutter/current/usr/include -cxx-isystem /snap/flutter/current/usr/include/x86_64-linux-gnu -cxx-isystem /snap/flutter/current/usr/include/c++/9 -internal-isystem /usr/local/include -internal-isystem /snap/flutter/149/usr/lib/llvm-10/lib/clang/10.0.0/include -internal-externc-isystem /usr/include/x86_64-linux-gnu -internal-externc-isystem /include -internal-externc-isystem /usr/include -fdebug-compilation-dir /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/CMakeFiles/CMakeTmp -ferror-limit 19 -fmessage-length 0 -fgnuc-version=4.2.1 -fobjc-runtime=gcc -fdiagnostics-show-option -faddrsig -o CMakeFiles/cmTC_74c10.dir/CMakeCCompilerABI.c.o -x c /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCCompilerABI.c]
  ignore line: [clang -cc1 version 10.0.0 based upon LLVM 10.0.0 default target x86_64-pc-linux-gnu]
  ignore line: [ignoring nonexistent directory "/include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ /usr/local/include]
  ignore line: [ /snap/flutter/149/usr/lib/llvm-10/lib/clang/10.0.0/include]
  ignore line: [ /usr/include/x86_64-linux-gnu]
  ignore line: [ /usr/include]
  ignore line: [End of search list.]
  ignore line: [[2/2] Linking C executable cmTC_74c10]
  ignore line: [clang version 10.0.0-4ubuntu1 ]
  ignore line: [Target: x86_64-pc-linux-gnu]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: /snap/flutter/current/usr/bin]
  ignore line: [Found candidate GCC installation: /snap/flutter/current/usr/bin/../lib/gcc/x86_64-linux-gnu/10]
  ignore line: [Found candidate GCC installation: /snap/flutter/current/usr/bin/../lib/gcc/x86_64-linux-gnu/9]
  ignore line: [Found candidate GCC installation: /usr/lib/gcc/x86_64-linux-gnu/13]
  ignore line: [Selected GCC installation: /usr/lib/gcc/x86_64-linux-gnu/13]
  ignore line: [Candidate multilib: .]
  ignore line: [@m64]
  ignore line: [Selected multilib: .]
  ignore line: [@m64]
  link line: [ "/snap/flutter/current/usr/bin/ld" --build-id --eh-frame-hdr -m elf_x86_64 -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_74c10 /snap/flutter/current/usr/lib/x86_64-linux-gnu/crt1.o /snap/flutter/current/usr/lib/x86_64-linux-gnu/crti.o /snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9/crtbegin.o -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib/ -L/usr/lib/gcc/x86_64-linux-gnu/13 -L/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/13/../../../../lib64 -L/lib/x86_64-linux-gnu -L/lib/../lib64 -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib64 -L/usr/lib/x86_64-linux-gnu/../../lib64 -L/usr/lib/gcc/x86_64-linux-gnu/13/../../.. -L/snap/flutter/149/usr/lib/llvm-10/bin/../lib -L/lib -L/usr/lib -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib -lblkid -lgcrypt -llzma -llz4 -lgpg-error -luuid -lpthread -ldl -lepoxy -lfontconfig CMakeFiles/cmTC_74c10.dir/CMakeCCompilerABI.c.o -lgcc --as-needed -lgcc_s --no-as-needed -lc -lgcc --as-needed -lgcc_s --no-as-needed /snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9/crtend.o /snap/flutter/current/usr/lib/x86_64-linux-gnu/crtn.o]
    arg [/snap/flutter/current/usr/bin/ld] ==> ignore
    arg [--build-id] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-m] ==> ignore
    arg [elf_x86_64] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/lib64/ld-linux-x86-64.so.2] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_74c10] ==> ignore
    arg [/snap/flutter/current/usr/lib/x86_64-linux-gnu/crt1.o] ==> ignore
    arg [/snap/flutter/current/usr/lib/x86_64-linux-gnu/crti.o] ==> ignore
    arg [/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9/crtbegin.o] ==> ignore
    arg [-L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9] ==> dir [/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9]
    arg [-L/snap/flutter/current/usr/lib/x86_64-linux-gnu] ==> dir [/snap/flutter/current/usr/lib/x86_64-linux-gnu]
    arg [-L/snap/flutter/current/lib/x86_64-linux-gnu] ==> dir [/snap/flutter/current/lib/x86_64-linux-gnu]
    arg [-L/snap/flutter/current/usr/lib/] ==> dir [/snap/flutter/current/usr/lib/]
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/13] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/13]
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu]
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/13/../../../../lib64] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/13/../../../../lib64]
    arg [-L/lib/x86_64-linux-gnu] ==> dir [/lib/x86_64-linux-gnu]
    arg [-L/lib/../lib64] ==> dir [/lib/../lib64]
    arg [-L/usr/lib/x86_64-linux-gnu] ==> dir [/usr/lib/x86_64-linux-gnu]
    arg [-L/usr/lib/../lib64] ==> dir [/usr/lib/../lib64]
    arg [-L/usr/lib/x86_64-linux-gnu/../../lib64] ==> dir [/usr/lib/x86_64-linux-gnu/../../lib64]
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/13/../../..] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/13/../../..]
    arg [-L/snap/flutter/149/usr/lib/llvm-10/bin/../lib] ==> dir [/snap/flutter/149/usr/lib/llvm-10/bin/../lib]
    arg [-L/lib] ==> dir [/lib]
    arg [-L/usr/lib] ==> dir [/usr/lib]
    arg [-L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9] ==> dir [/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9]
    arg [-L/snap/flutter/current/usr/lib/x86_64-linux-gnu] ==> dir [/snap/flutter/current/usr/lib/x86_64-linux-gnu]
    arg [-L/snap/flutter/current/lib/x86_64-linux-gnu] ==> dir [/snap/flutter/current/lib/x86_64-linux-gnu]
    arg [-L/snap/flutter/current/usr/lib] ==> dir [/snap/flutter/current/usr/lib]
    arg [-lblkid] ==> lib [blkid]
    arg [-lgcrypt] ==> lib [gcrypt]
    arg [-llzma] ==> lib [lzma]
    arg [-llz4] ==> lib [lz4]
    arg [-lgpg-error] ==> lib [gpg-error]
    arg [-luuid] ==> lib [uuid]
    arg [-lpthread] ==> lib [pthread]
    arg [-ldl] ==> lib [dl]
    arg [-lepoxy] ==> lib [epoxy]
    arg [-lfontconfig] ==> lib [fontconfig]
    arg [CMakeFiles/cmTC_74c10.dir/CMakeCCompilerABI.c.o] ==> ignore
    arg [-lgcc] ==> lib [gcc]
    arg [--as-needed] ==> ignore
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [--no-as-needed] ==> ignore
    arg [-lc] ==> lib [c]
    arg [-lgcc] ==> lib [gcc]
    arg [--as-needed] ==> ignore
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [--no-as-needed] ==> ignore
    arg [/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9/crtend.o] ==> ignore
    arg [/snap/flutter/current/usr/lib/x86_64-linux-gnu/crtn.o] ==> ignore
  collapse library dir [/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9] ==> [/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9]
  collapse library dir [/snap/flutter/current/usr/lib/x86_64-linux-gnu] ==> [/snap/flutter/current/usr/lib/x86_64-linux-gnu]
  collapse library dir [/snap/flutter/current/lib/x86_64-linux-gnu] ==> [/snap/flutter/current/lib/x86_64-linux-gnu]
  collapse library dir [/snap/flutter/current/usr/lib/] ==> [/snap/flutter/current/usr/lib]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/13] ==> [/usr/lib/gcc/x86_64-linux-gnu/13]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/13/../../../../lib64] ==> [/usr/lib64]
  collapse library dir [/lib/x86_64-linux-gnu] ==> [/lib/x86_64-linux-gnu]
  collapse library dir [/lib/../lib64] ==> [/lib64]
  collapse library dir [/usr/lib/x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
  collapse library dir [/usr/lib/../lib64] ==> [/usr/lib64]
  collapse library dir [/usr/lib/x86_64-linux-gnu/../../lib64] ==> [/usr/lib64]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/13/../../..] ==> [/usr/lib]
  collapse library dir [/snap/flutter/149/usr/lib/llvm-10/bin/../lib] ==> [/snap/flutter/149/usr/lib/llvm-10/lib]
  collapse library dir [/lib] ==> [/lib]
  collapse library dir [/usr/lib] ==> [/usr/lib]
  collapse library dir [/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9] ==> [/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9]
  collapse library dir [/snap/flutter/current/usr/lib/x86_64-linux-gnu] ==> [/snap/flutter/current/usr/lib/x86_64-linux-gnu]
  collapse library dir [/snap/flutter/current/lib/x86_64-linux-gnu] ==> [/snap/flutter/current/lib/x86_64-linux-gnu]
  collapse library dir [/snap/flutter/current/usr/lib] ==> [/snap/flutter/current/usr/lib]
  implicit libs: [blkid;gcrypt;lzma;lz4;gpg-error;uuid;pthread;dl;epoxy;fontconfig;gcc;gcc_s;c;gcc;gcc_s]
  implicit dirs: [/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9;/snap/flutter/current/usr/lib/x86_64-linux-gnu;/snap/flutter/current/lib/x86_64-linux-gnu;/snap/flutter/current/usr/lib;/usr/lib/gcc/x86_64-linux-gnu/13;/usr/lib/x86_64-linux-gnu;/usr/lib64;/lib/x86_64-linux-gnu;/lib64;/usr/lib;/snap/flutter/149/usr/lib/llvm-10/lib;/lib]
  implicit fwks: []


