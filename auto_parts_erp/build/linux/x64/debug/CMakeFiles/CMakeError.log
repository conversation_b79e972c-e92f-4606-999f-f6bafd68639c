Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: /snap/flutter/current/usr/bin/clang++ 
Build flags: 
Id flags:  

The output was:
1
/snap/flutter/current/usr/bin/ld: /lib/x86_64-linux-gnu/libm.so.6: unknown type [0x13] section `.relr.dyn'
/snap/flutter/current/usr/bin/ld: skipping incompatible /lib/x86_64-linux-gnu/libm.so.6 when searching for /lib/x86_64-linux-gnu/libm.so.6
/snap/flutter/current/usr/bin/ld: cannot find /lib/x86_64-linux-gnu/libm.so.6
/snap/flutter/current/usr/bin/ld: /lib/x86_64-linux-gnu/libmvec.so.1: unknown type [0x13] section `.relr.dyn'
/snap/flutter/current/usr/bin/ld: skipping incompatible /lib/x86_64-linux-gnu/libmvec.so.1 when searching for /lib/x86_64-linux-gnu/libmvec.so.1
/snap/flutter/current/usr/bin/ld: cannot find /lib/x86_64-linux-gnu/libmvec.so.1
clang: error: linker command failed with exit code 1 (use -v to see invocation)


Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: /snap/flutter/current/usr/bin/clang 
Build flags: 
Id flags:  

The output was:
1
/snap/flutter/current/usr/bin/ld: /lib/x86_64-linux-gnu/libc.so.6: unknown type [0x13] section `.relr.dyn'
/snap/flutter/current/usr/bin/ld: skipping incompatible /lib/x86_64-linux-gnu/libc.so.6 when searching for /lib/x86_64-linux-gnu/libc.so.6
/snap/flutter/current/usr/bin/ld: cannot find /lib/x86_64-linux-gnu/libc.so.6
/snap/flutter/current/usr/bin/ld: /lib64/ld-linux-x86-64.so.2: unknown type [0x13] section `.relr.dyn'
/snap/flutter/current/usr/bin/ld: skipping incompatible /lib64/ld-linux-x86-64.so.2 when searching for /lib64/ld-linux-x86-64.so.2
/snap/flutter/current/usr/bin/ld: cannot find /lib64/ld-linux-x86-64.so.2
clang: error: linker command failed with exit code 1 (use -v to see invocation)


