# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.16

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: runner
# Configuration: Debug
# =============================================================================
# =============================================================================

#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__auto_parts_erp
  depfile = $DEP_FILE
  deps = gcc
  command = /snap/flutter/current/usr/bin/clang++  $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX executable.

rule CXX_EXECUTABLE_LINKER__auto_parts_erp
  command = $PRE_LINK && /snap/flutter/current/usr/bin/clang++  $FLAGS  $LINK_FLAGS $in  -o $TARGET_FILE $LINK_PATH $LINK_LIBRARIES && $POST_BUILD
  description = Linking CXX executable $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__file_selector_linux_plugin
  depfile = $DEP_FILE
  deps = gcc
  command = /snap/flutter/current/usr/bin/clang++  $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX shared library.

rule CXX_SHARED_LIBRARY_LINKER__file_selector_linux_plugin
  command = $PRE_LINK && /snap/flutter/current/usr/bin/clang++ -fPIC $LANGUAGE_COMPILE_FLAGS $ARCH_FLAGS $LINK_FLAGS -shared $SONAME_FLAG$SONAME -o $TARGET_FILE $in $LINK_PATH $LINK_LIBRARIES && $POST_BUILD
  description = Linking CXX shared library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__printing_plugin
  depfile = $DEP_FILE
  deps = gcc
  command = /snap/flutter/current/usr/bin/clang++  $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX shared library.

rule CXX_SHARED_LIBRARY_LINKER__printing_plugin
  command = $PRE_LINK && /snap/flutter/current/usr/bin/clang++ -fPIC $LANGUAGE_COMPILE_FLAGS $ARCH_FLAGS $LINK_FLAGS -shared $SONAME_FLAG$SONAME -o $TARGET_FILE $in $LINK_PATH $LINK_LIBRARIES && $POST_BUILD
  description = Linking CXX shared library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__screen_retriever_plugin
  depfile = $DEP_FILE
  deps = gcc
  command = /snap/flutter/current/usr/bin/clang++  $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX shared library.

rule CXX_SHARED_LIBRARY_LINKER__screen_retriever_plugin
  command = $PRE_LINK && /snap/flutter/current/usr/bin/clang++ -fPIC $LANGUAGE_COMPILE_FLAGS $ARCH_FLAGS $LINK_FLAGS -shared $SONAME_FLAG$SONAME -o $TARGET_FILE $in $LINK_PATH $LINK_LIBRARIES && $POST_BUILD
  description = Linking CXX shared library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__sqlite3_flutter_libs_plugin
  depfile = $DEP_FILE
  deps = gcc
  command = /snap/flutter/current/usr/bin/clang $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out   -c $in
  description = Building C object $out


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__sqlite3_flutter_libs_plugin
  depfile = $DEP_FILE
  deps = gcc
  command = /snap/flutter/current/usr/bin/clang++  $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX shared library.

rule CXX_SHARED_LIBRARY_LINKER__sqlite3_flutter_libs_plugin
  command = $PRE_LINK && /snap/flutter/current/usr/bin/clang++ -fPIC $LANGUAGE_COMPILE_FLAGS $ARCH_FLAGS $LINK_FLAGS -shared $SONAME_FLAG$SONAME -o $TARGET_FILE $in $LINK_PATH $LINK_LIBRARIES && $POST_BUILD
  description = Linking CXX shared library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__window_manager_plugin
  depfile = $DEP_FILE
  deps = gcc
  command = /snap/flutter/current/usr/bin/clang++  $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX shared library.

rule CXX_SHARED_LIBRARY_LINKER__window_manager_plugin
  command = $PRE_LINK && /snap/flutter/current/usr/bin/clang++ -fPIC $LANGUAGE_COMPILE_FLAGS $ARCH_FLAGS $LINK_FLAGS -shared $SONAME_FLAG$SONAME -o $TARGET_FILE $in $LINK_PATH $LINK_LIBRARIES && $POST_BUILD
  description = Linking CXX shared library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = /snap/flutter/149/usr/bin/cmake -S/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/linux -B/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = /snap/flutter/current/usr/bin/ninja -t clean
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = /snap/flutter/current/usr/bin/ninja -t targets
  description = All primary targets available:

