# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.16

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: sqlite3-populate
# Configuration: 
# =============================================================================
# =============================================================================

#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = /snap/flutter/149/usr/bin/cmake -S/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild -B/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = /snap/flutter/current/usr/bin/ninja -t clean
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = /snap/flutter/current/usr/bin/ninja -t targets
  description = All primary targets available:

