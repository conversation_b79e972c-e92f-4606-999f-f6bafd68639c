# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.16

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: sqlite3-populate
# Configuration: 
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include rules.ninja


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild && /snap/flutter/149/usr/bin/cmake -S/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild -B/home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild && /snap/flutter/149/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for sqlite3-populate

build sqlite3-populate: phony CMakeFiles/sqlite3-populate CMakeFiles/sqlite3-populate-complete sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-done sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-install sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-mkdir sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-download sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-update sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-patch sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-configure sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-build sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-test


#############################################
# Phony custom command for CMakeFiles/sqlite3-populate

build CMakeFiles/sqlite3-populate: phony CMakeFiles/sqlite3-populate-complete


#############################################
# Custom command for CMakeFiles/sqlite3-populate-complete

build CMakeFiles/sqlite3-populate-complete sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-done: CUSTOM_COMMAND sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-install sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-mkdir sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-download sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-update sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-patch sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-configure sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-build sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-install sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-test
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild && /snap/flutter/149/usr/bin/cmake -E make_directory /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild/CMakeFiles && /snap/flutter/149/usr/bin/cmake -E touch /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild/CMakeFiles/sqlite3-populate-complete && /snap/flutter/149/usr/bin/cmake -E touch /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild/sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-done
  DESC = Completed 'sqlite3-populate'
  restat = 1


#############################################
# Custom command for sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-install

build sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-install: CUSTOM_COMMAND sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-build
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-build && /snap/flutter/149/usr/bin/cmake -E echo_append && /snap/flutter/149/usr/bin/cmake -E touch /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild/sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-install
  DESC = No install step for 'sqlite3-populate'
  restat = 1


#############################################
# Custom command for sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-mkdir

build sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-mkdir: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild && /snap/flutter/149/usr/bin/cmake -E make_directory /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-src && /snap/flutter/149/usr/bin/cmake -E make_directory /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-build && /snap/flutter/149/usr/bin/cmake -E make_directory /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild/sqlite3-populate-prefix && /snap/flutter/149/usr/bin/cmake -E make_directory /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild/sqlite3-populate-prefix/tmp && /snap/flutter/149/usr/bin/cmake -E make_directory /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild/sqlite3-populate-prefix/src/sqlite3-populate-stamp && /snap/flutter/149/usr/bin/cmake -E make_directory /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild/sqlite3-populate-prefix/src && /snap/flutter/149/usr/bin/cmake -E make_directory /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild/sqlite3-populate-prefix/src/sqlite3-populate-stamp && /snap/flutter/149/usr/bin/cmake -E touch /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild/sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-mkdir
  DESC = Creating directories for 'sqlite3-populate'
  restat = 1


#############################################
# Custom command for sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-download

build sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-download: CUSTOM_COMMAND sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-urlinfo.txt sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-mkdir
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps && /snap/flutter/149/usr/bin/cmake -P /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild/sqlite3-populate-prefix/src/sqlite3-populate-stamp/download-sqlite3-populate.cmake && /snap/flutter/149/usr/bin/cmake -P /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild/sqlite3-populate-prefix/src/sqlite3-populate-stamp/verify-sqlite3-populate.cmake && /snap/flutter/149/usr/bin/cmake -P /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild/sqlite3-populate-prefix/src/sqlite3-populate-stamp/extract-sqlite3-populate.cmake && /snap/flutter/149/usr/bin/cmake -E touch /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild/sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-download
  DESC = Performing download step (download, verify and extract) for 'sqlite3-populate'
  pool = console
  restat = 1


#############################################
# Custom command for sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-update

build sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-update: CUSTOM_COMMAND sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-download
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild && /snap/flutter/149/usr/bin/cmake -E echo_append && /snap/flutter/149/usr/bin/cmake -E touch /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild/sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-update
  DESC = No update step for 'sqlite3-populate'
  pool = console
  restat = 1


#############################################
# Custom command for sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-patch

build sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-patch: CUSTOM_COMMAND sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-download
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild && /snap/flutter/149/usr/bin/cmake -E echo_append && /snap/flutter/149/usr/bin/cmake -E touch /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild/sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-patch
  DESC = No patch step for 'sqlite3-populate'
  restat = 1


#############################################
# Custom command for sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-configure

build sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-configure: CUSTOM_COMMAND sqlite3-populate-prefix/tmp/sqlite3-populate-cfgcmd.txt sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-update sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-patch
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-build && /snap/flutter/149/usr/bin/cmake -E echo_append && /snap/flutter/149/usr/bin/cmake -E touch /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild/sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-configure
  DESC = No configure step for 'sqlite3-populate'
  restat = 1


#############################################
# Custom command for sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-build

build sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-build: CUSTOM_COMMAND sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-configure
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-build && /snap/flutter/149/usr/bin/cmake -E echo_append && /snap/flutter/149/usr/bin/cmake -E touch /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild/sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-build
  DESC = No build step for 'sqlite3-populate'
  restat = 1


#############################################
# Custom command for sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-test

build sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-test: CUSTOM_COMMAND sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-install
  COMMAND = cd /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-build && /snap/flutter/149/usr/bin/cmake -E echo_append && /snap/flutter/149/usr/bin/cmake -E touch /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild/sqlite3-populate-prefix/src/sqlite3-populate-stamp/sqlite3-populate-test
  DESC = No test step for 'sqlite3-populate'
  restat = 1

# =============================================================================
# Target aliases.

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /home/<USER>/dev/dimensions_ERP_Flutter/auto_parts_erp/build/linux/x64/debug/_deps/sqlite3-subbuild

build all: phony sqlite3-populate

# =============================================================================
# Built-in targets


#############################################
# Make the all target the default.

default all

#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeDetermineSystem.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeGenericSystem.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeInitializeConfigs.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeSystem.cmake.in /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeSystemSpecificInformation.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeSystemSpecificInitialize.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/ExternalProject-download.cmake.in /snap/flutter/149/usr/share/cmake-3.16/Modules/ExternalProject.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/UnixPaths.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/RepositoryInfo.txt.in CMakeCache.txt CMakeFiles/3.16.3/CMakeSystem.cmake CMakeLists.txt sqlite3-populate-prefix/tmp/sqlite3-populate-cfgcmd.txt.in
  pool = console


#############################################
# A missing CMake input file is not an error.

build /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeDetermineSystem.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeGenericSystem.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeInitializeConfigs.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeSystem.cmake.in /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeSystemSpecificInformation.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeSystemSpecificInitialize.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/ExternalProject-download.cmake.in /snap/flutter/149/usr/share/cmake-3.16/Modules/ExternalProject.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/UnixPaths.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/RepositoryInfo.txt.in CMakeCache.txt CMakeFiles/3.16.3/CMakeSystem.cmake CMakeLists.txt sqlite3-populate-prefix/tmp/sqlite3-populate-cfgcmd.txt.in: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP

