import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:window_manager/window_manager.dart';
import 'core/theme/warp_theme.dart';
import 'core/widgets/warp_sidebar/warp_sidebar.dart';
import 'core/widgets/warp_sidebar/sidebar_controller.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize window manager for desktop
  await windowManager.ensureInitialized();
  
  WindowOptions windowOptions = const WindowOptions(
    size: Size(1400, 900),
    minimumSize: Size(1200, 700),
    center: true,
    backgroundColor: Colors.transparent,
    skipTaskbar: false,
    titleBarStyle: TitleBarStyle.normal,
    windowButtonVisibility: true,
  );
  
  windowManager.waitUntilReadyToShow(windowOptions, () async {
    await windowManager.show();
    await windowManager.focus();
  });

  runApp(const ProviderScope(child: AutoPartsERPApp()));
}

class AutoPartsERPApp extends ConsumerWidget {
  const AutoPartsERPApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MaterialApp(
      title: 'Auto Parts ERP - نظام إدارة قطع الغيار',
      debugShowCheckedModeBanner: false,
      theme: WarpTheme.lightTheme,
      darkTheme: WarpTheme.darkTheme,
      themeMode: ThemeMode.dark, // Default to dark theme for Warp 2.0 aesthetic
      home: const MainLayout(),
    );
  }
}

class MainLayout extends ConsumerWidget {
  const MainLayout({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sidebarState = ref.watch(sidebarControllerProvider);
    
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: Row(
        children: [
          // Warp 2.0 Sidebar
          WarpSidebar(
            showSearchBar: true,
            onSearchTap: () {
              // TODO: Implement search functionality
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    sidebarState.isRTL 
                        ? 'البحث السريع قريباً...' 
                        : 'Quick search coming soon...',
                  ),
                  backgroundColor: WarpTheme.primary,
                ),
              );
            },
            footer: _buildSidebarFooter(context, ref),
          ),
          
          // Main content area
          Expanded(
            child: Container(
              margin: const EdgeInsets.only(top: 16, right: 16, bottom: 16),
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  bottomLeft: Radius.circular(20),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 20,
                    offset: const Offset(-5, 0),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  bottomLeft: Radius.circular(20),
                ),
                child: _buildMainContent(context, ref),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSidebarFooter(BuildContext context, WidgetRef ref) {
    final sidebarState = ref.watch(sidebarControllerProvider);
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // User profile section
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                WarpTheme.primary.withOpacity(0.1),
                WarpTheme.accent.withOpacity(0.1),
              ],
            ),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: WarpTheme.primary.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: WarpTheme.primary,
                child: Text(
                  'A',
                  style: theme.textTheme.titleSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              if (sidebarState.isExpanded || sidebarState.isHovered) ...[
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        sidebarState.isRTL ? 'المدير العام' : 'Admin User',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: isDark ? Colors.white : Colors.black87,
                          fontWeight: FontWeight.w600,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        sidebarState.isRTL ? 'متصل' : 'Online',
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: WarpTheme.success,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
        
        const SizedBox(height: 8),
        
        // Version info
        if (sidebarState.isExpanded || sidebarState.isHovered)
          Text(
            'v1.0.0 Beta',
            style: theme.textTheme.labelSmall?.copyWith(
              color: isDark ? Colors.white38 : Colors.black38,
            ),
          ),
      ],
    );
  }

  Widget _buildMainContent(BuildContext context, WidgetRef ref) {
    final sidebarState = ref.watch(sidebarControllerProvider);
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      sidebarState.isRTL 
                          ? 'مرحباً بك في نظام إدارة قطع الغيار'
                          : 'Welcome to Auto Parts ERP System',
                      style: theme.textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      sidebarState.isRTL 
                          ? 'نظام متقدم لإدارة قطع الغيار والزيوت والإطارات والبطاريات'
                          : 'Advanced management system for auto parts, oils, tires, and batteries',
                      style: theme.textTheme.bodyLarge?.copyWith(
                        color: theme.textTheme.bodyLarge?.color?.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ),
              
              // Quick actions
              Row(
                children: [
                  _buildQuickActionButton(
                    context,
                    icon: Icons.notifications_rounded,
                    label: sidebarState.isRTL ? 'الإشعارات' : 'Notifications',
                    badgeCount: 3,
                    onTap: () {},
                  ),
                  const SizedBox(width: 12),
                  _buildQuickActionButton(
                    context,
                    icon: Icons.settings_rounded,
                    label: sidebarState.isRTL ? 'الإعدادات' : 'Settings',
                    onTap: () {},
                  ),
                ],
              ),
            ],
          ),
          
          const SizedBox(height: 32),
          
          // Dashboard content placeholder
          Expanded(
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    WarpTheme.primary.withOpacity(0.1),
                    WarpTheme.accent.withOpacity(0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: WarpTheme.primary.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.dashboard_rounded,
                      size: 64,
                      color: WarpTheme.primary.withOpacity(0.5),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      sidebarState.isRTL 
                          ? 'لوحة التحكم الرئيسية'
                          : 'Main Dashboard',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        color: theme.textTheme.headlineSmall?.color?.withOpacity(0.7),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      sidebarState.isRTL 
                          ? 'سيتم تطوير المحتوى قريباً...'
                          : 'Content coming soon...',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.textTheme.bodyMedium?.color?.withOpacity(0.5),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    int? badgeCount,
    required VoidCallback onTap,
  }) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                WarpTheme.primary.withOpacity(0.1),
                WarpTheme.primary.withOpacity(0.05),
              ],
            ),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: WarpTheme.primary.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: onTap,
              borderRadius: BorderRadius.circular(12),
              child: Container(
                padding: const EdgeInsets.all(12),
                child: Icon(
                  icon,
                  size: 24,
                  color: WarpTheme.primary,
                ),
              ),
            ),
          ),
        ),
        
        if (badgeCount != null && badgeCount > 0)
          Positioned(
            top: 0,
            right: 0,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: WarpTheme.danger,
                borderRadius: BorderRadius.circular(10),
                boxShadow: WarpTheme.getNeonGlow(WarpTheme.danger, intensity: 0.3),
              ),
              child: Text(
                badgeCount > 99 ? '99+' : badgeCount.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
      ],
    );
  }
}
