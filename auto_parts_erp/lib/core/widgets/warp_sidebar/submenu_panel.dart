import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../theme/warp_theme.dart';
import 'glass_container.dart';
import 'warp_animations.dart';
import 'sidebar_controller.dart';

/// Sliding submenu panel widget
class SubmenuPanel extends ConsumerStatefulWidget {
  final MenuItem parentMenuItem;
  final bool isVisible;
  final bool isRTL;
  final Offset position;
  final VoidCallback? onClose;

  const SubmenuPanel({
    super.key,
    required this.parentMenuItem,
    this.isVisible = false,
    this.isRTL = false,
    this.position = Offset.zero,
    this.onClose,
  });

  @override
  ConsumerState<SubmenuPanel> createState() => _SubmenuPanelState();
}

class _SubmenuPanelState extends ConsumerState<SubmenuPanel>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: WarpTheme.warpDuration,
      vsync: this,
    );
    
    _slideAnimation = WarpAnimations.createSlideAnimation(
      _animationController,
      isRTL: widget.isRTL,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: WarpTheme.warpCurve,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: WarpTheme.warpBounceCurve,
    ));
    
    if (widget.isVisible) {
      _animationController.forward();
    }
  }

  @override
  void didUpdateWidget(SubmenuPanel oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isVisible != oldWidget.isVisible) {
      if (widget.isVisible) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.parentMenuItem.subItems == null || 
        widget.parentMenuItem.subItems!.isEmpty) {
      return const SizedBox.shrink();
    }

    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final screenSize = MediaQuery.of(context).size;
    
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Positioned(
          left: widget.isRTL ? null : widget.position.dx,
          right: widget.isRTL ? screenSize.width - widget.position.dx : null,
          top: widget.position.dy,
          child: SlideTransition(
            position: _slideAnimation,
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: ScaleTransition(
                scale: _scaleAnimation,
                alignment: widget.isRTL ? Alignment.centerRight : Alignment.centerLeft,
                child: MouseRegion(
                  onExit: (_) {
                    // Delay closing to allow mouse movement between sidebar and submenu
                    Future.delayed(const Duration(milliseconds: 100), () {
                      if (mounted) {
                        widget.onClose?.call();
                      }
                    });
                  },
                  child: GlassContainer(
                    width: WarpTheme.submenuPanelWidth,
                    padding: const EdgeInsets.all(12),
                    borderRadius: 16,
                    withGlow: true,
                    glowColor: widget.parentMenuItem.iconColor ?? WarpTheme.primary,
                    glowIntensity: 0.3,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header
                        _buildHeader(theme, isDark),
                        
                        const SizedBox(height: 12),
                        
                        // Submenu items
                        StaggeredListAnimation(
                          isVisible: widget.isVisible,
                          children: widget.parentMenuItem.subItems!
                              .map((subItem) => _buildSubmenuItem(subItem, theme, isDark))
                              .toList(),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(ThemeData theme, bool isDark) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            (widget.parentMenuItem.iconColor ?? WarpTheme.primary).withOpacity(0.2),
            (widget.parentMenuItem.iconColor ?? WarpTheme.primary).withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: (widget.parentMenuItem.iconColor ?? WarpTheme.primary).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            widget.parentMenuItem.icon,
            size: 20,
            color: widget.parentMenuItem.iconColor ?? WarpTheme.primary,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              widget.isRTL 
                  ? widget.parentMenuItem.titleAr 
                  : widget.parentMenuItem.titleEn,
              style: theme.textTheme.titleSmall?.copyWith(
                color: isDark ? Colors.white : Colors.black87,
                fontWeight: FontWeight.w600,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (widget.parentMenuItem.isPremium)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [WarpTheme.accent, WarpTheme.secondary],
                ),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                'PRO',
                style: theme.textTheme.labelSmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 8,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSubmenuItem(MenuItem subItem, ThemeData theme, bool isDark) {
    final sidebarState = ref.watch(sidebarControllerProvider);
    final isActive = sidebarState.activeMenuId == subItem.id;
    
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      child: AnimatedGlassContainer(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
        borderRadius: 12,
        withGlow: isActive,
        glowColor: subItem.iconColor ?? widget.parentMenuItem.iconColor ?? WarpTheme.primary,
        glowIntensity: 0.4,
        hoverScale: 1.02,
        onTap: () {
          ref.read(sidebarControllerProvider.notifier).setActiveMenu(subItem.id);
          subItem.onTap?.call();
          widget.onClose?.call();
        },
        child: Row(
          children: [
            // Icon
            Icon(
              subItem.icon,
              size: 18,
              color: isActive 
                  ? (subItem.iconColor ?? widget.parentMenuItem.iconColor ?? WarpTheme.primary)
                  : (isDark ? Colors.white70 : Colors.black54),
            ),
            
            const SizedBox(width: 12),
            
            // Title
            Expanded(
              child: Text(
                widget.isRTL ? subItem.titleAr : subItem.titleEn,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: isActive 
                      ? (subItem.iconColor ?? widget.parentMenuItem.iconColor ?? WarpTheme.primary)
                      : (isDark ? Colors.white : Colors.black87),
                  fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            
            // Badge
            if (subItem.showBadge && subItem.badgeCount > 0)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: WarpTheme.danger,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: WarpTheme.getNeonGlow(WarpTheme.danger, intensity: 0.3),
                ),
                child: Text(
                  subItem.badgeCount > 99 ? '99+' : subItem.badgeCount.toString(),
                  style: theme.textTheme.labelSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 9,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// Submenu overlay manager
class SubmenuOverlay {
  static OverlayEntry? _currentOverlay;
  
  static void show({
    required BuildContext context,
    required MenuItem parentMenuItem,
    required Offset position,
    bool isRTL = false,
    VoidCallback? onClose,
  }) {
    hide(); // Hide any existing overlay
    
    _currentOverlay = OverlayEntry(
      builder: (context) => SubmenuPanel(
        parentMenuItem: parentMenuItem,
        isVisible: true,
        isRTL: isRTL,
        position: position,
        onClose: () {
          hide();
          onClose?.call();
        },
      ),
    );
    
    Overlay.of(context).insert(_currentOverlay!);
  }
  
  static void hide() {
    _currentOverlay?.remove();
    _currentOverlay = null;
  }
  
  static bool get isVisible => _currentOverlay != null;
}

/// Submenu trigger widget for collapsed sidebar
class SubmenuTrigger extends ConsumerStatefulWidget {
  final MenuItem menuItem;
  final bool isRTL;
  final Widget child;

  const SubmenuTrigger({
    super.key,
    required this.menuItem,
    this.isRTL = false,
    required this.child,
  });

  @override
  ConsumerState<SubmenuTrigger> createState() => _SubmenuTriggerState();
}

class _SubmenuTriggerState extends ConsumerState<SubmenuTrigger> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    if (widget.menuItem.subItems == null || widget.menuItem.subItems!.isEmpty) {
      return widget.child;
    }

    return MouseRegion(
      onEnter: (event) {
        setState(() {
          _isHovered = true;
        });
        
        // Show submenu after a short delay
        Future.delayed(const Duration(milliseconds: 300), () {
          if (_isHovered && mounted) {
            final RenderBox renderBox = context.findRenderObject() as RenderBox;
            final position = renderBox.localToGlobal(Offset.zero);
            
            SubmenuOverlay.show(
              context: context,
              parentMenuItem: widget.menuItem,
              position: Offset(
                widget.isRTL 
                    ? position.dx - WarpTheme.submenuPanelWidth - 10
                    : position.dx + WarpTheme.sidebarCollapsedWidth + 10,
                position.dy,
              ),
              isRTL: widget.isRTL,
              onClose: () {
                setState(() {
                  _isHovered = false;
                });
              },
            );
          }
        });
      },
      onExit: (event) {
        setState(() {
          _isHovered = false;
        });
        
        // Hide submenu after a short delay
        Future.delayed(const Duration(milliseconds: 200), () {
          if (!_isHovered && mounted) {
            SubmenuOverlay.hide();
          }
        });
      },
      child: widget.child,
    );
  }
}
