import 'package:flutter/material.dart';
import '../../theme/warp_theme.dart';

/// Custom animation controllers and curves for Warp 2.0 sidebar
class WarpAnimations {
  /// Stagger animation for menu items
  static List<Animation<double>> createStaggeredAnimations({
    required AnimationController controller,
    required int itemCount,
    Duration delay = const Duration(milliseconds: 50),
  }) {
    final animations = <Animation<double>>[];
    final delayInterval = delay.inMilliseconds / controller.duration!.inMilliseconds;
    
    for (int i = 0; i < itemCount; i++) {
      final start = i * delayInterval;
      final end = start + (1.0 - start) / 2;
      
      animations.add(
        Tween<double>(begin: 0.0, end: 1.0).animate(
          CurvedAnimation(
            parent: controller,
            curve: Interval(start, end.clamp(0.0, 1.0), curve: WarpTheme.warpCurve),
          ),
        ),
      );
    }
    
    return animations;
  }

  /// Icon rotation animation
  static Animation<double> createIconRotationAnimation(AnimationController controller) {
    return Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: controller,
        curve: WarpTheme.warpBounceCurve,
      ),
    );
  }

  /// Glow intensity animation
  static Animation<double> createGlowAnimation(AnimationController controller) {
    return Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(
        parent: controller,
        curve: Curves.easeInOut,
      ),
    );
  }

  /// Slide animation for submenu
  static Animation<Offset> createSlideAnimation(
    AnimationController controller, {
    bool isRTL = false,
  }) {
    return Tween<Offset>(
      begin: Offset(isRTL ? -1.0 : 1.0, 0.0),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: controller,
        curve: WarpTheme.warpCurve,
      ),
    );
  }

  /// Scale animation for hover effects
  static Animation<double> createScaleAnimation(AnimationController controller) {
    return Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(
        parent: controller,
        curve: WarpTheme.warpCurve,
      ),
    );
  }
}

/// Animated icon with rotation and glow effects
class WarpAnimatedIcon extends StatefulWidget {
  final IconData icon;
  final double size;
  final Color? color;
  final bool isActive;
  final bool shouldRotate;
  final Duration animationDuration;

  const WarpAnimatedIcon({
    super.key,
    required this.icon,
    this.size = 24,
    this.color,
    this.isActive = false,
    this.shouldRotate = true,
    this.animationDuration = WarpTheme.warpDuration,
  });

  @override
  State<WarpAnimatedIcon> createState() => _WarpAnimatedIconState();
}

class _WarpAnimatedIconState extends State<WarpAnimatedIcon>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _rotationAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _rotationAnimation = WarpAnimations.createIconRotationAnimation(_controller);
    _glowAnimation = WarpAnimations.createGlowAnimation(_controller);
    
    if (widget.isActive) {
      _controller.forward();
    }
  }

  @override
  void didUpdateWidget(WarpAnimatedIcon oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isActive != oldWidget.isActive) {
      if (widget.isActive) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            boxShadow: widget.isActive
                ? WarpTheme.getNeonGlow(
                    widget.color ?? WarpTheme.primary,
                    intensity: _glowAnimation.value * 0.5,
                  )
                : null,
          ),
          child: Transform.rotate(
            angle: widget.shouldRotate ? _rotationAnimation.value * 2 * 3.14159 : 0,
            child: Icon(
              widget.icon,
              size: widget.size,
              color: widget.isActive
                  ? (widget.color ?? WarpTheme.primary)
                  : (widget.color ?? Colors.white70),
            ),
          ),
        );
      },
    );
  }
}

/// Staggered list animation
class StaggeredListAnimation extends StatefulWidget {
  final List<Widget> children;
  final Duration duration;
  final Duration delay;
  final bool isVisible;

  const StaggeredListAnimation({
    super.key,
    required this.children,
    this.duration = WarpTheme.warpDuration,
    this.delay = const Duration(milliseconds: 50),
    this.isVisible = true,
  });

  @override
  State<StaggeredListAnimation> createState() => _StaggeredListAnimationState();
}

class _StaggeredListAnimationState extends State<StaggeredListAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    
    _animations = WarpAnimations.createStaggeredAnimations(
      controller: _controller,
      itemCount: widget.children.length,
      delay: widget.delay,
    );
    
    if (widget.isVisible) {
      _controller.forward();
    }
  }

  @override
  void didUpdateWidget(StaggeredListAnimation oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isVisible != oldWidget.isVisible) {
      if (widget.isVisible) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: widget.children.asMap().entries.map((entry) {
        final index = entry.key;
        final child = entry.value;
        
        return AnimatedBuilder(
          animation: _animations[index],
          builder: (context, _) {
            return Transform.translate(
              offset: Offset(0, 20 * (1 - _animations[index].value)),
              child: Opacity(
                opacity: _animations[index].value,
                child: child,
              ),
            );
          },
        );
      }).toList(),
    );
  }
}

/// Ripple animation effect
class RippleAnimation extends StatefulWidget {
  final Widget child;
  final Color rippleColor;
  final Duration duration;
  final VoidCallback? onTap;

  const RippleAnimation({
    super.key,
    required this.child,
    this.rippleColor = WarpTheme.primary,
    this.duration = const Duration(milliseconds: 600),
    this.onTap,
  });

  @override
  State<RippleAnimation> createState() => _RippleAnimationState();
}

class _RippleAnimationState extends State<RippleAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _startRipple() {
    _controller.reset();
    _controller.forward();
    widget.onTap?.call();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _startRipple,
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return CustomPaint(
            painter: RipplePainter(
              animation: _animation,
              color: widget.rippleColor,
            ),
            child: widget.child,
          );
        },
      ),
    );
  }
}

/// Custom painter for ripple effect
class RipplePainter extends CustomPainter {
  final Animation<double> animation;
  final Color color;

  RipplePainter({
    required this.animation,
    required this.color,
  }) : super(repaint: animation);

  @override
  void paint(Canvas canvas, Size size) {
    if (animation.value > 0) {
      final center = Offset(size.width / 2, size.height / 2);
      final radius = size.width * animation.value;
      final opacity = 1.0 - animation.value;
      
      final paint = Paint()
        ..color = color.withOpacity(opacity * 0.3)
        ..style = PaintingStyle.fill;
      
      canvas.drawCircle(center, radius, paint);
    }
  }

  @override
  bool shouldRepaint(RipplePainter oldDelegate) {
    return animation.value != oldDelegate.animation.value;
  }
}
