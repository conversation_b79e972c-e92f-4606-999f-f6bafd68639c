import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../theme/warp_theme.dart';
import 'glass_container.dart';
import 'warp_animations.dart';
import 'sidebar_controller.dart';

/// Individual menu item widget with hover effects and animations
class MenuItemWidget extends ConsumerStatefulWidget {
  final MenuItem menuItem;
  final bool isCollapsed;
  final bool isRTL;
  final VoidCallback? onTap;
  final VoidCallback? onHover;

  const MenuItemWidget({
    super.key,
    required this.menuItem,
    this.isCollapsed = false,
    this.isRTL = false,
    this.onTap,
    this.onHover,
  });

  @override
  ConsumerState<MenuItemWidget> createState() => _MenuItemWidgetState();
}

class _MenuItemWidgetState extends ConsumerState<MenuItemWidget>
    with SingleTickerProviderStateMixin {
  bool _isHovered = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: WarpTheme.warpDuration,
      vsync: this,
    );
    
    _scaleAnimation = WarpAnimations.createScaleAnimation(_animationController);
    _glowAnimation = WarpAnimations.createGlowAnimation(_animationController);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onHover(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
    });
    
    if (isHovered) {
      _animationController.forward();
      widget.onHover?.call();
    } else {
      _animationController.reverse();
    }
  }

  void _onTap() {
    // Ripple effect
    _animationController.reset();
    _animationController.forward();
    
    // Set active menu
    ref.read(sidebarControllerProvider.notifier).setActiveMenu(widget.menuItem.id);
    
    widget.onTap?.call();
    widget.menuItem.onTap?.call();
  }

  @override
  Widget build(BuildContext context) {
    final sidebarState = ref.watch(sidebarControllerProvider);
    final isActive = sidebarState.activeMenuId == widget.menuItem.id;
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            child: MouseRegion(
              onEnter: (_) => _onHover(true),
              onExit: (_) => _onHover(false),
              child: GlassContainer(
                padding: EdgeInsets.symmetric(
                  horizontal: widget.isCollapsed ? 12 : 16,
                  vertical: 12,
                ),
                borderRadius: 16,
                withGlow: isActive || _isHovered,
                glowColor: widget.menuItem.iconColor ?? WarpTheme.primary,
                glowIntensity: _glowAnimation.value,
                isHovered: _isHovered,
                onTap: _onTap,
                child: Row(
                  children: [
                    // Icon with animation
                    WarpAnimatedIcon(
                      icon: widget.menuItem.icon,
                      size: 24,
                      color: isActive 
                          ? (widget.menuItem.iconColor ?? WarpTheme.primary)
                          : (isDark ? Colors.white70 : Colors.black54),
                      isActive: isActive,
                      shouldRotate: _isHovered,
                    ),
                    
                    // Title and badge (only when expanded)
                    if (!widget.isCollapsed) ...[
                      const SizedBox(width: 16),
                      Expanded(
                        child: Row(
                          children: [
                            // Title
                            Expanded(
                              child: Text(
                                widget.isRTL 
                                    ? widget.menuItem.titleAr 
                                    : widget.menuItem.titleEn,
                                style: theme.textTheme.titleMedium?.copyWith(
                                  color: isActive 
                                      ? (widget.menuItem.iconColor ?? WarpTheme.primary)
                                      : (isDark ? Colors.white : Colors.black87),
                                  fontWeight: isActive ? FontWeight.w600 : FontWeight.w500,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            
                            // Premium badge
                            if (widget.menuItem.isPremium)
                              Container(
                                margin: const EdgeInsets.only(left: 8),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 6,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  gradient: const LinearGradient(
                                    colors: [WarpTheme.accent, WarpTheme.secondary],
                                  ),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  'PRO',
                                  style: theme.textTheme.labelSmall?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 10,
                                  ),
                                ),
                              ),
                            
                            // Badge count
                            if (widget.menuItem.showBadge && widget.menuItem.badgeCount > 0)
                              Container(
                                margin: const EdgeInsets.only(left: 8),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: WarpTheme.danger,
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: WarpTheme.getNeonGlow(
                                    WarpTheme.danger,
                                    intensity: 0.3,
                                  ),
                                ),
                                child: Text(
                                  widget.menuItem.badgeCount > 99 
                                      ? '99+' 
                                      : widget.menuItem.badgeCount.toString(),
                                  style: theme.textTheme.labelSmall?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 10,
                                  ),
                                ),
                              ),
                            
                            // Submenu arrow
                            if (widget.menuItem.subItems != null && widget.menuItem.subItems!.isNotEmpty)
                              Icon(
                                widget.isRTL 
                                    ? Icons.keyboard_arrow_left_rounded
                                    : Icons.keyboard_arrow_right_rounded,
                                size: 20,
                                color: isDark ? Colors.white54 : Colors.black54,
                              ),
                          ],
                        ),
                      ),
                    ],
                    

                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Favorite menu item widget with star indicator
class FavoriteMenuItemWidget extends ConsumerWidget {
  final MenuItem menuItem;
  final bool isCollapsed;
  final bool isRTL;

  const FavoriteMenuItemWidget({
    super.key,
    required this.menuItem,
    this.isCollapsed = false,
    this.isRTL = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // For now, just show the menu item without the star overlay to avoid layout issues
    return MenuItemWidget(
      menuItem: menuItem,
      isCollapsed: isCollapsed,
      isRTL: isRTL,
    );
  }
}

/// Menu section header
class MenuSectionHeader extends StatelessWidget {
  final String titleAr;
  final String titleEn;
  final bool isRTL;
  final bool isCollapsed;

  const MenuSectionHeader({
    super.key,
    required this.titleAr,
    required this.titleEn,
    this.isRTL = false,
    this.isCollapsed = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isCollapsed) {
      return const SizedBox(height: 16);
    }
    
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 1,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.transparent,
                    (isDark ? Colors.white : Colors.black).withOpacity(0.2),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 12),
            child: Text(
              isRTL ? titleAr : titleEn,
              style: theme.textTheme.labelSmall?.copyWith(
                color: isDark ? Colors.white54 : Colors.black54,
                fontWeight: FontWeight.w600,
                letterSpacing: 1.2,
              ),
            ),
          ),
          Expanded(
            child: Container(
              height: 1,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.transparent,
                    (isDark ? Colors.white : Colors.black).withOpacity(0.2),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
