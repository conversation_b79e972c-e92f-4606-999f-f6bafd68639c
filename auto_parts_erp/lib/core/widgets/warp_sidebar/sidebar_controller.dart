import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Menu item model for sidebar navigation
class MenuItem {
  final String id;
  final String titleAr;
  final String titleEn;
  final IconData icon;
  final Color? iconColor;
  final List<MenuItem>? subItems;
  final String? route;
  final bool showBadge;
  final int badgeCount;
  final bool isPremium;
  final bool isActive;
  final VoidCallback? onTap;

  const MenuItem({
    required this.id,
    required this.titleAr,
    required this.titleEn,
    required this.icon,
    this.iconColor,
    this.subItems,
    this.route,
    this.showBadge = false,
    this.badgeCount = 0,
    this.isPremium = false,
    this.isActive = false,
    this.onTap,
  });

  MenuItem copyWith({
    String? id,
    String? titleAr,
    String? titleEn,
    IconData? icon,
    Color? iconColor,
    List<MenuItem>? subItems,
    String? route,
    bool? showBadge,
    int? badgeCount,
    bool? isPremium,
    bool? isActive,
    VoidCallback? onTap,
  }) {
    return MenuItem(
      id: id ?? this.id,
      titleAr: titleAr ?? this.titleAr,
      titleEn: titleEn ?? this.titleEn,
      icon: icon ?? this.icon,
      iconColor: iconColor ?? this.iconColor,
      subItems: subItems ?? this.subItems,
      route: route ?? this.route,
      showBadge: showBadge ?? this.showBadge,
      badgeCount: badgeCount ?? this.badgeCount,
      isPremium: isPremium ?? this.isPremium,
      isActive: isActive ?? this.isActive,
      onTap: onTap ?? this.onTap,
    );
  }
}

/// Sidebar state management
class SidebarState {
  final bool isExpanded;
  final bool isHovered;
  final String? activeMenuId;
  final String? hoveredMenuId;
  final bool isRTL;
  final List<MenuItem> menuItems;
  final List<String> favoriteItems;

  const SidebarState({
    this.isExpanded = false,
    this.isHovered = false,
    this.activeMenuId,
    this.hoveredMenuId,
    this.isRTL = false,
    this.menuItems = const [],
    this.favoriteItems = const [],
  });

  SidebarState copyWith({
    bool? isExpanded,
    bool? isHovered,
    String? activeMenuId,
    String? hoveredMenuId,
    bool? isRTL,
    List<MenuItem>? menuItems,
    List<String>? favoriteItems,
  }) {
    return SidebarState(
      isExpanded: isExpanded ?? this.isExpanded,
      isHovered: isHovered ?? this.isHovered,
      activeMenuId: activeMenuId ?? this.activeMenuId,
      hoveredMenuId: hoveredMenuId ?? this.hoveredMenuId,
      isRTL: isRTL ?? this.isRTL,
      menuItems: menuItems ?? this.menuItems,
      favoriteItems: favoriteItems ?? this.favoriteItems,
    );
  }
}

/// Sidebar controller using Riverpod
class SidebarController extends StateNotifier<SidebarState> {
  SidebarController() : super(const SidebarState()) {
    _loadPreferences();
    _initializeMenuItems();
  }

  /// Load user preferences
  Future<void> _loadPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isExpanded = prefs.getBool('sidebar_expanded') ?? false;
      final isRTL = prefs.getBool('is_rtl') ?? false;
      final favoriteItems = prefs.getStringList('favorite_items') ?? [];
      
      state = state.copyWith(
        isExpanded: isExpanded,
        isRTL: isRTL,
        favoriteItems: favoriteItems,
      );
    } catch (e) {
      debugPrint('Error loading sidebar preferences: $e');
    }
  }

  /// Save user preferences
  Future<void> _savePreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('sidebar_expanded', state.isExpanded);
      await prefs.setBool('is_rtl', state.isRTL);
      await prefs.setStringList('favorite_items', state.favoriteItems);
    } catch (e) {
      debugPrint('Error saving sidebar preferences: $e');
    }
  }

  /// Initialize menu items based on the Arabic configuration
  void _initializeMenuItems() {
    final menuItems = [
      // 1. Dashboard
      MenuItem(
        id: 'dashboard',
        titleAr: 'الرئيسية',
        titleEn: 'Dashboard',
        icon: Icons.dashboard_rounded,
        iconColor: const Color(0xFF00D4FF),
        route: '/dashboard',
        subItems: [
          MenuItem(
            id: 'dashboard_overview',
            titleAr: 'لوحة المعلومات',
            titleEn: 'Overview',
            icon: Icons.analytics_rounded,
            route: '/dashboard/overview',
          ),
          MenuItem(
            id: 'dashboard_reports',
            titleAr: 'تقارير سريعة',
            titleEn: 'Quick Reports',
            icon: Icons.speed_rounded,
            route: '/dashboard/quick-reports',
          ),
        ],
      ),
      
      // 2. Sales
      MenuItem(
        id: 'sales',
        titleAr: 'المبيعات',
        titleEn: 'Sales',
        icon: Icons.point_of_sale_rounded,
        iconColor: const Color(0xFF10B981),
        showBadge: true,
        badgeCount: 5,
        subItems: [
          MenuItem(
            id: 'pos',
            titleAr: 'نقطة البيع',
            titleEn: 'POS',
            icon: Icons.shopping_cart_rounded,
            route: '/sales/pos',
          ),
          MenuItem(
            id: 'invoices',
            titleAr: 'الفواتير',
            titleEn: 'Invoices',
            icon: Icons.receipt_long_rounded,
            route: '/sales/invoices',
          ),
          MenuItem(
            id: 'quotes',
            titleAr: 'عروض الأسعار',
            titleEn: 'Quotes',
            icon: Icons.request_quote_rounded,
            route: '/sales/quotes',
          ),
          MenuItem(
            id: 'returns',
            titleAr: 'المرتجعات',
            titleEn: 'Returns',
            icon: Icons.keyboard_return_rounded,
            route: '/sales/returns',
          ),
          MenuItem(
            id: 'customers',
            titleAr: 'العملاء',
            titleEn: 'Customers',
            icon: Icons.people_rounded,
            route: '/sales/customers',
          ),
        ],
      ),
      
      // 3. Purchases
      MenuItem(
        id: 'purchases',
        titleAr: 'المشتريات',
        titleEn: 'Purchases',
        icon: Icons.shopping_bag_rounded,
        iconColor: const Color(0xFFFF006E),
        subItems: [
          MenuItem(
            id: 'purchase_orders',
            titleAr: 'أوامر الشراء',
            titleEn: 'Purchase Orders',
            icon: Icons.add_shopping_cart_rounded,
            route: '/purchases/orders',
          ),
          MenuItem(
            id: 'purchase_invoices',
            titleAr: 'فواتير المشتريات',
            titleEn: 'Purchase Invoices',
            icon: Icons.receipt_rounded,
            route: '/purchases/invoices',
          ),
          MenuItem(
            id: 'suppliers',
            titleAr: 'الموردين',
            titleEn: 'Suppliers',
            icon: Icons.business_rounded,
            route: '/purchases/suppliers',
          ),
        ],
      ),
      
      // 4. Inventory
      MenuItem(
        id: 'inventory',
        titleAr: 'المخزون',
        titleEn: 'Inventory',
        icon: Icons.inventory_2_rounded,
        iconColor: const Color(0xFFF59E0B),
        showBadge: true,
        badgeCount: 12,
        subItems: [
          MenuItem(
            id: 'products',
            titleAr: 'المنتجات',
            titleEn: 'Products',
            icon: Icons.category_rounded,
            route: '/inventory/products',
          ),
          MenuItem(
            id: 'stock_management',
            titleAr: 'إدارة المخزون',
            titleEn: 'Stock Management',
            icon: Icons.warehouse_rounded,
            route: '/inventory/stock',
          ),
          MenuItem(
            id: 'transfers',
            titleAr: 'التحويلات',
            titleEn: 'Transfers',
            icon: Icons.swap_horiz_rounded,
            route: '/inventory/transfers',
          ),
          MenuItem(
            id: 'stock_count',
            titleAr: 'الجرد',
            titleEn: 'Stock Count',
            icon: Icons.fact_check_rounded,
            route: '/inventory/count',
          ),
        ],
      ),
      
      // 5. Finance
      MenuItem(
        id: 'finance',
        titleAr: 'المالية',
        titleEn: 'Finance',
        icon: Icons.account_balance_rounded,
        iconColor: const Color(0xFF8B5CF6),
        subItems: [
          MenuItem(
            id: 'accounts',
            titleAr: 'الحسابات',
            titleEn: 'Accounts',
            icon: Icons.account_tree_rounded,
            route: '/finance/accounts',
          ),
          MenuItem(
            id: 'cash_register',
            titleAr: 'الصندوق',
            titleEn: 'Cash Register',
            icon: Icons.money_rounded,
            route: '/finance/cash',
          ),
          MenuItem(
            id: 'banks',
            titleAr: 'البنوك',
            titleEn: 'Banks',
            icon: Icons.account_balance_wallet_rounded,
            route: '/finance/banks',
          ),
          MenuItem(
            id: 'expenses',
            titleAr: 'المصروفات',
            titleEn: 'Expenses',
            icon: Icons.payment_rounded,
            route: '/finance/expenses',
          ),
        ],
      ),
      
      // 6. Shared Economy
      MenuItem(
        id: 'shared_economy',
        titleAr: 'الاقتصاد التشاركي',
        titleEn: 'Shared Economy',
        icon: Icons.share_rounded,
        iconColor: const Color(0xFF06B6D4),
        isPremium: true,
        subItems: [
          MenuItem(
            id: 'marketplace',
            titleAr: 'السوق المشترك',
            titleEn: 'Marketplace',
            icon: Icons.storefront_rounded,
            route: '/shared/marketplace',
          ),
          MenuItem(
            id: 'partners',
            titleAr: 'الشركاء',
            titleEn: 'Partners',
            icon: Icons.handshake_rounded,
            route: '/shared/partners',
          ),
          MenuItem(
            id: 'shared_transactions',
            titleAr: 'المعاملات المشتركة',
            titleEn: 'Shared Transactions',
            icon: Icons.sync_alt_rounded,
            route: '/shared/transactions',
          ),
        ],
      ),
      
      // 7. Reports
      MenuItem(
        id: 'reports',
        titleAr: 'التقارير',
        titleEn: 'Reports',
        icon: Icons.bar_chart_rounded,
        iconColor: const Color(0xFFEF4444),
        subItems: [
          MenuItem(
            id: 'sales_reports',
            titleAr: 'تقارير المبيعات',
            titleEn: 'Sales Reports',
            icon: Icons.trending_up_rounded,
            route: '/reports/sales',
          ),
          MenuItem(
            id: 'inventory_reports',
            titleAr: 'تقارير المخزون',
            titleEn: 'Inventory Reports',
            icon: Icons.inventory_rounded,
            route: '/reports/inventory',
          ),
          MenuItem(
            id: 'financial_reports',
            titleAr: 'التقارير المالية',
            titleEn: 'Financial Reports',
            icon: Icons.account_balance_rounded,
            route: '/reports/financial',
          ),
        ],
      ),
      
      // 8. Settings
      MenuItem(
        id: 'settings',
        titleAr: 'الإعدادات',
        titleEn: 'Settings',
        icon: Icons.settings_rounded,
        iconColor: const Color(0xFF6B7280),
        subItems: [
          MenuItem(
            id: 'company_settings',
            titleAr: 'إعدادات الشركة',
            titleEn: 'Company Settings',
            icon: Icons.business_center_rounded,
            route: '/settings/company',
          ),
          MenuItem(
            id: 'system_settings',
            titleAr: 'إعدادات النظام',
            titleEn: 'System Settings',
            icon: Icons.tune_rounded,
            route: '/settings/system',
          ),
          MenuItem(
            id: 'integrations',
            titleAr: 'التكاملات',
            titleEn: 'Integrations',
            icon: Icons.extension_rounded,
            route: '/settings/integrations',
          ),
          MenuItem(
            id: 'backup',
            titleAr: 'النسخ الاحتياطي',
            titleEn: 'Backup',
            icon: Icons.backup_rounded,
            route: '/settings/backup',
          ),
        ],
      ),
    ];

    state = state.copyWith(menuItems: menuItems);
  }

  /// Toggle sidebar expansion
  void toggleExpansion() {
    state = state.copyWith(isExpanded: !state.isExpanded);
    _savePreferences();
  }

  /// Set hover state
  void setHovered(bool isHovered) {
    state = state.copyWith(isHovered: isHovered);
  }

  /// Set active menu item
  void setActiveMenu(String menuId) {
    state = state.copyWith(activeMenuId: menuId);
  }

  /// Set hovered menu item
  void setHoveredMenu(String? menuId) {
    state = state.copyWith(hoveredMenuId: menuId);
  }

  /// Toggle RTL mode
  void toggleRTL() {
    state = state.copyWith(isRTL: !state.isRTL);
    _savePreferences();
  }

  /// Add item to favorites
  void addToFavorites(String menuId) {
    if (!state.favoriteItems.contains(menuId)) {
      final newFavorites = [...state.favoriteItems, menuId];
      state = state.copyWith(favoriteItems: newFavorites);
      _savePreferences();
    }
  }

  /// Remove item from favorites
  void removeFromFavorites(String menuId) {
    final newFavorites = state.favoriteItems.where((id) => id != menuId).toList();
    state = state.copyWith(favoriteItems: newFavorites);
    _savePreferences();
  }

  /// Get menu item by ID
  MenuItem? getMenuItemById(String id) {
    for (final item in state.menuItems) {
      if (item.id == id) return item;
      if (item.subItems != null) {
        for (final subItem in item.subItems!) {
          if (subItem.id == id) return subItem;
        }
      }
    }
    return null;
  }
}

/// Sidebar controller provider
final sidebarControllerProvider = StateNotifierProvider<SidebarController, SidebarState>(
  (ref) => SidebarController(),
);
