import 'package:flutter/material.dart';
import 'dart:ui';
import '../../theme/warp_theme.dart';

/// Reusable glass morphism container with Warp 2.0 styling
class GlassContainer extends StatelessWidget {
  final Widget child;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double borderRadius;
  final bool withGlow;
  final Color? glowColor;
  final double glowIntensity;
  final bool isDark;
  final VoidCallback? onTap;
  final bool isHovered;
  final double blurSigma;

  const GlassContainer({
    super.key,
    required this.child,
    this.width,
    this.height,
    this.padding,
    this.margin,
    this.borderRadius = 20,
    this.withGlow = false,
    this.glowColor,
    this.glowIntensity = 0.5,
    this.isDark = true,
    this.onTap,
    this.isHovered = false,
    this.blurSigma = 10,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final actualIsDark = isDark || theme.brightness == Brightness.dark;
    
    return Container(
      width: width,
      height: height,
      margin: margin,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          // Base shadow
          BoxShadow(
            color: actualIsDark 
                ? Colors.black.withOpacity(0.3)
                : Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
          // Glow effect
          if (withGlow || isHovered)
            ...WarpTheme.getNeonGlow(
              glowColor ?? WarpTheme.primary,
              intensity: isHovered ? glowIntensity * 1.5 : glowIntensity,
            ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: blurSigma, sigmaY: blurSigma),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: actualIsDark
                    ? [
                        Colors.white.withOpacity(isHovered ? 0.15 : 0.1),
                        Colors.white.withOpacity(isHovered ? 0.08 : 0.05),
                      ]
                    : [
                        Colors.black.withOpacity(isHovered ? 0.08 : 0.05),
                        Colors.black.withOpacity(isHovered ? 0.04 : 0.02),
                      ],
              ),
              borderRadius: BorderRadius.circular(borderRadius),
              border: Border.all(
                color: actualIsDark 
                    ? Colors.white.withOpacity(isHovered ? 0.3 : 0.2)
                    : Colors.black.withOpacity(isHovered ? 0.15 : 0.1),
                width: isHovered ? 2.0 : 1.5,
              ),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: onTap,
                borderRadius: BorderRadius.circular(borderRadius),
                splashColor: (glowColor ?? WarpTheme.primary).withOpacity(0.2),
                highlightColor: (glowColor ?? WarpTheme.primary).withOpacity(0.1),
                child: Container(
                  padding: padding,
                  child: child,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// Animated glass container with hover effects
class AnimatedGlassContainer extends StatefulWidget {
  final Widget child;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double borderRadius;
  final bool withGlow;
  final Color? glowColor;
  final double glowIntensity;
  final bool isDark;
  final VoidCallback? onTap;
  final Duration animationDuration;
  final double hoverScale;

  const AnimatedGlassContainer({
    super.key,
    required this.child,
    this.width,
    this.height,
    this.padding,
    this.margin,
    this.borderRadius = 20,
    this.withGlow = false,
    this.glowColor,
    this.glowIntensity = 0.5,
    this.isDark = true,
    this.onTap,
    this.animationDuration = const Duration(milliseconds: 200),
    this.hoverScale = 1.05,
  });

  @override
  State<AnimatedGlassContainer> createState() => _AnimatedGlassContainerState();
}

class _AnimatedGlassContainerState extends State<AnimatedGlassContainer>
    with SingleTickerProviderStateMixin {
  bool _isHovered = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: widget.hoverScale,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: WarpTheme.warpCurve,
    ));
    
    _glowAnimation = Tween<double>(
      begin: widget.glowIntensity,
      end: widget.glowIntensity * 1.5,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: WarpTheme.warpCurve,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onHover(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
    });
    
    if (isHovered) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => _onHover(true),
      onExit: (_) => _onHover(false),
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: GlassContainer(
              width: widget.width,
              height: widget.height,
              padding: widget.padding,
              margin: widget.margin,
              borderRadius: widget.borderRadius,
              withGlow: widget.withGlow,
              glowColor: widget.glowColor,
              glowIntensity: _glowAnimation.value,
              isDark: widget.isDark,
              onTap: widget.onTap,
              isHovered: _isHovered,
              child: widget.child,
            ),
          );
        },
      ),
    );
  }
}

/// Glass container with ripple effect
class RippleGlassContainer extends StatelessWidget {
  final Widget child;
  final VoidCallback? onTap;
  final double borderRadius;
  final EdgeInsetsGeometry? padding;
  final Color? rippleColor;

  const RippleGlassContainer({
    super.key,
    required this.child,
    this.onTap,
    this.borderRadius = 20,
    this.padding,
    this.rippleColor,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedGlassContainer(
      borderRadius: borderRadius,
      padding: padding,
      withGlow: true,
      glowColor: rippleColor ?? WarpTheme.primary,
      onTap: onTap,
      child: child,
    );
  }
}
