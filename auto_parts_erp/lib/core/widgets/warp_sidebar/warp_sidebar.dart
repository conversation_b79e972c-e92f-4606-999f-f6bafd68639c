import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../theme/warp_theme.dart';
import 'glass_container.dart';
import 'warp_animations.dart';
import 'sidebar_controller.dart';
import 'menu_item_widget.dart';
import 'submenu_panel.dart';

/// Main Warp 2.0 style sidebar navigation system
class WarpSidebar extends ConsumerStatefulWidget {
  final Widget? header;
  final Widget? footer;
  final bool showSearchBar;
  final VoidCallback? onSearchTap;
  final EdgeInsetsGeometry? margin;

  const WarpSidebar({
    super.key,
    this.header,
    this.footer,
    this.showSearchBar = true,
    this.onSearchTap,
    this.margin,
  });

  @override
  ConsumerState<WarpSidebar> createState() => _WarpSidebarState();
}

class _WarpSidebarState extends ConsumerState<WarpSidebar>
    with TickerProviderStateMixin {
  late AnimationController _expansionController;
  late AnimationController _hoverController;
  late Animation<double> _widthAnimation;
  late Animation<double> _glowAnimation;
  
  bool _isMouseInside = false;
  
  @override
  void initState() {
    super.initState();
    
    _expansionController = AnimationController(
      duration: WarpTheme.warpDuration,
      vsync: this,
    );
    
    _hoverController = AnimationController(
      duration: WarpTheme.warpFastDuration,
      vsync: this,
    );
    
    _widthAnimation = Tween<double>(
      begin: WarpTheme.sidebarCollapsedWidth,
      end: WarpTheme.sidebarExpandedWidth,
    ).animate(CurvedAnimation(
      parent: _expansionController,
      curve: WarpTheme.warpCurve,
    ));
    
    _glowAnimation = WarpAnimations.createGlowAnimation(_hoverController);
  }

  @override
  void dispose() {
    _expansionController.dispose();
    _hoverController.dispose();
    super.dispose();
  }

  void _onMouseEnter() {
    setState(() {
      _isMouseInside = true;
    });
    
    final sidebarState = ref.read(sidebarControllerProvider);
    if (!sidebarState.isExpanded) {
      _expansionController.forward();
    }
    _hoverController.forward();
    
    ref.read(sidebarControllerProvider.notifier).setHovered(true);
  }

  void _onMouseExit() {
    setState(() {
      _isMouseInside = false;
    });
    
    final sidebarState = ref.read(sidebarControllerProvider);
    if (!sidebarState.isExpanded) {
      _expansionController.reverse();
    }
    _hoverController.reverse();
    
    ref.read(sidebarControllerProvider.notifier).setHovered(false);
    
    // Hide any open submenus
    SubmenuOverlay.hide();
  }

  void _toggleExpansion() {
    final controller = ref.read(sidebarControllerProvider.notifier);
    controller.toggleExpansion();
    
    final sidebarState = ref.read(sidebarControllerProvider);
    if (sidebarState.isExpanded) {
      _expansionController.forward();
    } else {
      _expansionController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final sidebarState = ref.watch(sidebarControllerProvider);
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    // Update animation based on state
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (sidebarState.isExpanded && !_expansionController.isCompleted) {
        _expansionController.forward();
      } else if (!sidebarState.isExpanded && !_isMouseInside && _expansionController.isCompleted) {
        _expansionController.reverse();
      }
    });

    return AnimatedBuilder(
      animation: Listenable.merge([_expansionController, _hoverController]),
      builder: (context, child) {
        final isExpanded = _widthAnimation.value > WarpTheme.sidebarCollapsedWidth + 50;
        
        return Container(
          margin: widget.margin ?? const EdgeInsets.all(16),
          child: MouseRegion(
            onEnter: (_) => _onMouseEnter(),
            onExit: (_) => _onMouseExit(),
            child: GlassContainer(
              width: _widthAnimation.value,
              height: double.infinity,
              borderRadius: WarpTheme.sidebarBorderRadius,
              withGlow: _isMouseInside,
              glowColor: WarpTheme.primary,
              glowIntensity: _glowAnimation.value * 0.3,
              child: Column(
                children: [
                  // Header section
                  _buildHeader(isExpanded, isDark, sidebarState),
                  
                  // Search bar
                  if (widget.showSearchBar)
                    _buildSearchBar(isExpanded, isDark),
                  
                  // Menu items
                  Expanded(
                    child: _buildMenuItems(isExpanded, sidebarState, isDark),
                  ),
                  
                  // Footer section
                  if (widget.footer != null)
                    _buildFooter(isExpanded),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(bool isExpanded, bool isDark, SidebarState sidebarState) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Logo/Icon
          AnimatedGlassContainer(
            width: 40,
            height: 40,
            borderRadius: 12,
            withGlow: true,
            glowColor: WarpTheme.primary,
            glowIntensity: 0.5,
            onTap: _toggleExpansion,
            child: const Icon(
              Icons.auto_awesome_rounded,
              color: WarpTheme.primary,
              size: 24,
            ),
          ),
          
          // Title (only when expanded)
          if (isExpanded) ...[
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    sidebarState.isRTL ? 'نظام قطع الغيار' : 'Auto Parts ERP',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: isDark ? Colors.white : Colors.black87,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    sidebarState.isRTL ? 'إدارة متقدمة' : 'Advanced Management',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: isDark ? Colors.white70 : Colors.black54,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
          
          // Settings button (only when expanded)
          if (isExpanded)
            AnimatedGlassContainer(
              width: 32,
              height: 32,
              borderRadius: 8,
              onTap: () {
                ref.read(sidebarControllerProvider.notifier).toggleRTL();
              },
              child: Icon(
                sidebarState.isRTL ? Icons.translate_rounded : Icons.language_rounded,
                color: isDark ? Colors.white70 : Colors.black54,
                size: 18,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSearchBar(bool isExpanded, bool isDark) {
    if (!isExpanded) {
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: AnimatedGlassContainer(
          width: 40,
          height: 40,
          borderRadius: 12,
          onTap: widget.onSearchTap,
          child: Icon(
            Icons.search_rounded,
            color: isDark ? Colors.white70 : Colors.black54,
            size: 20,
          ),
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: AnimatedGlassContainer(
        height: 44,
        borderRadius: 12,
        padding: const EdgeInsets.symmetric(horizontal: 12),
        onTap: widget.onSearchTap,
        child: Row(
          children: [
            Icon(
              Icons.search_rounded,
              color: isDark ? Colors.white70 : Colors.black54,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                ref.watch(sidebarControllerProvider).isRTL 
                    ? 'بحث سريع... (Ctrl+K)'
                    : 'Quick search... (Ctrl+K)',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: isDark ? Colors.white54 : Colors.black54,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuItems(bool isExpanded, SidebarState sidebarState, bool isDark) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: StaggeredListAnimation(
        isVisible: true,
        children: [
          // Favorites section
          if (sidebarState.favoriteItems.isNotEmpty) ...[
            MenuSectionHeader(
              titleAr: 'المفضلة',
              titleEn: 'Favorites',
              isRTL: sidebarState.isRTL,
              isCollapsed: !isExpanded,
            ),
            ...sidebarState.favoriteItems
                .map((favoriteId) => sidebarState.menuItems
                    .expand((item) => [item, ...?item.subItems])
                    .where((item) => item.id == favoriteId)
                    .first)
                .map((item) => _buildMenuItem(item, isExpanded, sidebarState)),
            const SizedBox(height: 8),
          ],
          
          // Main menu items
          MenuSectionHeader(
            titleAr: 'القائمة الرئيسية',
            titleEn: 'Main Menu',
            isRTL: sidebarState.isRTL,
            isCollapsed: !isExpanded,
          ),
          
          ...sidebarState.menuItems.map((item) => 
              _buildMenuItem(item, isExpanded, sidebarState)),
        ],
      ),
    );
  }

  Widget _buildMenuItem(MenuItem item, bool isExpanded, SidebarState sidebarState) {
    if (!isExpanded && item.subItems != null && item.subItems!.isNotEmpty) {
      return SubmenuTrigger(
        menuItem: item,
        isRTL: sidebarState.isRTL,
        child: MenuItemWidget(
          menuItem: item,
          isCollapsed: !isExpanded,
          isRTL: sidebarState.isRTL,
        ),
      );
    }
    
    return MenuItemWidget(
      menuItem: item,
      isCollapsed: !isExpanded,
      isRTL: sidebarState.isRTL,
    );
  }

  Widget _buildFooter(bool isExpanded) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: widget.footer,
    );
  }
}

/// Keyboard shortcuts handler for sidebar
class SidebarShortcuts extends ConsumerWidget {
  final Widget child;

  const SidebarShortcuts({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return CallbackShortcuts(
      bindings: {
        // Toggle sidebar with Ctrl+B
        const SingleActivator(LogicalKeyboardKey.keyB, control: true): () {
          ref.read(sidebarControllerProvider.notifier).toggleExpansion();
        },
        
        // Quick search with Ctrl+K
        const SingleActivator(LogicalKeyboardKey.keyK, control: true): () {
          // Implement search functionality
        },
        
        // Toggle RTL with Ctrl+Shift+R
        const SingleActivator(
          LogicalKeyboardKey.keyR, 
          control: true, 
          shift: true,
        ): () {
          ref.read(sidebarControllerProvider.notifier).toggleRTL();
        },
      },
      child: Focus(
        autofocus: true,
        child: child,
      ),
    );
  }
}
