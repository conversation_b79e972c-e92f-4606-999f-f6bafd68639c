# auto_parts_erp

A new Flutter project.

## Getting Started

# Auto Parts ERP System - نظام إدارة قطع الغيار

A comprehensive Flutter Desktop ERP system for auto parts, oils, tires, and batteries companies with advanced Warp 2.0 design system.

## 🌟 Features

### 🎨 **Warp 2.0 Design System**
- **Futuristic Glass Morphism UI** with neon accent colors
- **Advanced Sidebar Navigation** with smooth hover animations
- **Multi-level menus** with elegant slide-out panels
- **RTL Support** for Arabic with proper animations mirroring
- **Dark/Light themes** with automatic system detection

### 🏢 **Core Business Modules**
- **📊 Dashboard** - Real-time analytics and KPIs
- **🛒 Sales & POS** - Desktop-optimized point of sale
- **🛍️ Purchases** - Supplier management and procurement
- **📦 Inventory** - Multi-branch stock management
- **💰 Finance** - Accounting and financial management
- **🤝 Shared Economy** - B2B marketplace for inventory sharing
- **📊 Reports** - Comprehensive business intelligence
- **⚙️ Settings** - System configuration and preferences

### 🔧 **Technical Features**
- **Multi-user & Multi-branch** support
- **Hybrid P2P + Central Server** architecture
- **Online/Offline mode** with automatic sync
- **PostgreSQL central + SQLite local** databases
- **WhatsApp Business API** integration
- **ZATCA (Saudi tax authority)** integration ready
- **QR code generation** for invoices
- **Barcode scanning** support

## 🚀 Getting Started

### Prerequisites
- Flutter 3.8.1 or higher
- Dart SDK 3.8.1 or higher
- Windows 10/11, macOS 10.14+, or Linux (Ubuntu 18.04+)
- PostgreSQL 15+ (for production)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-repo/auto_parts_erp.git
   cd auto_parts_erp
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Generate code**
   ```bash
   flutter packages pub run build_runner build
   ```

4. **Run the application**
   ```bash
   flutter run -d windows  # For Windows
   flutter run -d macos    # For macOS
   flutter run -d linux    # For Linux
   ```

## 🏗️ Project Structure

```
auto_parts_erp/
├── lib/
│   ├── core/                          # Core functionality
│   │   ├── constants/                 # App constants
│   │   ├── database/                  # Database layer
│   │   │   ├── local/                 # SQLite with Drift
│   │   │   └── remote/                # PostgreSQL connection
│   │   ├── localization/              # i18n support
│   │   ├── network/                   # HTTP client setup
│   │   ├── routes/                    # Navigation routes
│   │   ├── theme/                     # Warp 2.0 theme system
│   │   ├── utils/                     # Utility functions
│   │   └── widgets/                   # Reusable widgets
│   │       └── warp_sidebar/          # Advanced sidebar system
│   ├── features/                      # Feature modules
│   │   ├── auth/                      # Authentication
│   │   ├── products/                  # Product management
│   │   │   ├── data/                  # Data layer
│   │   │   ├── domain/                # Business logic
│   │   │   └── presentation/          # UI layer
│   │   ├── inventory/                 # Inventory management
│   │   ├── sales/                     # Sales & POS
│   │   ├── purchases/                 # Purchase management
│   │   ├── reports/                   # Business intelligence
│   │   ├── shared_economy/            # B2B marketplace
│   │   ├── sync/                      # Data synchronization
│   │   └── settings/                  # App settings
│   └── shared/                        # Shared components
├── assets/                            # Static assets
│   ├── images/                        # Image files
│   ├── fonts/                         # Custom fonts (Cairo, Roboto)
│   ├── translations/                  # i18n JSON files
│   └── icons/                         # App icons
├── windows/                           # Windows platform files
├── linux/                            # Linux platform files
└── macos/                            # macOS platform files
```

## 🎨 Warp 2.0 Design System

### Color Palette
```dart
// Primary colors
static const primary = Color(0xFF00D4FF);      // Cyan
static const secondary = Color(0xFFFF006E);    // Pink
static const accent = Color(0xFF8B5CF6);       // Purple
static const success = Color(0xFF10B981);      // Green
static const warning = Color(0xFFF59E0B);      // Amber
static const danger = Color(0xFFEF4444);       // Red
```

### Sidebar Features
- **Collapsed width**: 70px (icons only)
- **Expanded width**: 280px (full menu)
- **Submenu panel**: 250px (slides from right)
- **Animation duration**: 300ms with custom curves
- **Smart hover detection** with proximity sensing
- **Keyboard navigation** support (Tab, Arrow keys)
- **User preferences** persistence

## 📱 Key Modules Overview

### 1. 🏠 Dashboard (الرئيسية)
- Real-time business metrics
- Sales analytics with charts
- Inventory alerts
- Quick action buttons

### 2. 🛒 Sales (المبيعات)
- **POS Interface**: Desktop-optimized point of sale
- **Invoice Management**: Generate, print, and track invoices
- **Customer Management**: Complete customer database
- **Returns Processing**: Handle product returns

### 3. 📦 Inventory (المخزون)
- **Product Catalog**: Manage products with OEM numbers
- **Stock Tracking**: Multi-branch inventory levels
- **Car Compatibility**: Link products to vehicle models
- **Barcode Support**: Generate and scan barcodes

### 4. 🤝 Shared Economy (الاقتصاد التشاركي)
- **B2B Marketplace**: Share inventory with other companies
- **Quote Requests**: Request prices from network partners
- **Rating System**: Rate and review suppliers
- **Cross-network Search**: Find products across the network

## 🔧 Technical Architecture

### State Management
- **Riverpod** for reactive state management
- **Provider pattern** for dependency injection
- **Immutable state** with copyWith methods

### Database Layer
- **Local**: SQLite with Drift ORM
- **Remote**: PostgreSQL with connection pooling
- **Sync**: Automatic conflict resolution
- **Offline**: Queue-based sync mechanism

### Networking
- **Dio** HTTP client with interceptors
- **Retrofit** for type-safe API calls
- **JWT** authentication with refresh tokens
- **WebSocket** for real-time updates

## 🌍 Internationalization

The app supports both Arabic (RTL) and English (LTR) with:
- Complete UI translation
- RTL layout mirroring
- Arabic number formatting
- Cultural date/time formats
- Currency localization

## 🔒 Security Features

- **JWT Authentication** with secure token storage
- **Role-based permissions** system
- **Data encryption** for sensitive information
- **Audit logging** for all operations
- **ZATCA compliance** for Saudi tax requirements

## 📊 Database Schema Highlights

### Core Tables
- `products` - Product catalog with multilingual support
- `product_oem_numbers` - Multiple OEM numbers per product
- `product_car_compatibility` - Vehicle compatibility matrix
- `branch_inventory` - Distributed stock management
- `sync_queue` - Offline operation queue
- `shared_inventory` - B2B marketplace listings

## 🚀 Performance Optimizations

- **RepaintBoundary** for animation optimization
- **Lazy loading** for large datasets
- **Image caching** with automatic cleanup
- **Database indexing** for fast queries
- **Memory management** for large inventories

## 🧪 Testing

```bash
# Run all tests
flutter test

# Run integration tests
flutter test integration_test/

# Generate coverage report
flutter test --coverage
```

## 📦 Building for Production

### Windows
```bash
flutter build windows --release
```

### macOS
```bash
flutter build macos --release
```

### Linux
```bash
flutter build linux --release
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/autopartserp)
- 📖 Documentation: [docs.autopartserp.com](https://docs.autopartserp.com)

## 🙏 Acknowledgments

- Flutter team for the amazing framework
- Riverpod for excellent state management
- Drift for powerful SQLite ORM
- The open-source community for inspiration

---

**Made with ❤️ for the auto parts industry**
