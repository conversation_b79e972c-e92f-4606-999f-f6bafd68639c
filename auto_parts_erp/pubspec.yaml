name: auto_parts_erp
description: "Comprehensive Flutter Desktop ERP system for auto parts, oils, tires, and batteries companies"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter

  # State Management
  flutter_riverpod: ^2.5.1
  riverpod_annotation: ^2.3.5

  # Database
  drift: ^2.18.0
  sqlite3_flutter_libs: ^0.5.24
  path_provider: ^2.1.3
  path: ^1.9.0

  # Network
  dio: ^5.4.3+1
  retrofit: ^4.1.0
  json_annotation: ^4.9.0

  # Authentication
  jwt_decoder: ^2.0.1
  crypto: ^3.0.3

  # Localization
  flutter_localizations:
    sdk: flutter
  intl: ^0.20.2

  # UI Components
  flutter_staggered_grid_view: ^0.7.0
  data_table_2: ^2.5.15

  # Charts and Analytics
  fl_chart: ^0.68.0

  # PDF and Printing
  pdf: ^3.11.1
  printing: ^5.12.0

  # QR Code
  qr_flutter: ^4.1.0
  mobile_scanner: ^5.1.1

  # File Handling
  file_picker: ^8.0.6
  image_picker: ^1.1.2

  # Utilities
  uuid: ^4.4.2
  logger: ^2.4.0
  shared_preferences: ^2.2.3
  package_info_plus: ^8.0.0
  device_info_plus: ^10.1.2
  connectivity_plus: ^6.0.5

  # Desktop specific
  window_manager: ^0.3.9

  # Icons
  cupertino_icons: ^1.0.8

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code Generation
  build_runner: ^2.4.12
  drift_dev: ^2.18.0
  riverpod_generator: ^2.4.0
  retrofit_generator: ^8.1.0
  json_serializable: ^6.8.0

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/translations/
    - assets/icons/

  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
        - asset: assets/fonts/Cairo-Light.ttf
          weight: 300
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
        - asset: assets/fonts/Roboto-Light.ttf
          weight: 300
