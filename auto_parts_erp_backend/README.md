# Auto Parts ERP Backend API

High-performance Dart backend API server for the Auto Parts ERP system with PostgreSQL, Redis, and Docker support.

## 🚀 Quick Start

### Using Docker Compose (Recommended)

1. **Clone and navigate to backend directory**
   ```bash
   cd auto_parts_erp_backend
   ```

2. **Start all services**
   ```bash
   # Production mode
   docker-compose up -d

   # Development mode (includes pg<PERSON><PERSON><PERSON> and Redis Commander)
   docker-compose --profile development up -d
   ```

3. **Check service health**
   ```bash
   docker-compose ps
   curl http://localhost/health
   ```

### Manual Setup

1. **Install dependencies**
   ```bash
   dart pub get
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start PostgreSQL and Redis**
   ```bash
   # Using Docker
   docker run -d --name postgres -p 5432:5432 -e POSTGRES_DB=auto_parts_erp postgres:15
   docker run -d --name redis -p 6379:6379 redis:7-alpine
   ```

4. **Run database migrations**
   ```bash
   dart run bin/migrate.dart
   ```

5. **Start the server**
   ```bash
   dart run bin/server.dart
   ```

## 🏗️ Architecture

### Tech Stack
- **Runtime**: Dart 3.8.1+
- **Web Framework**: Shelf with Router
- **Database**: PostgreSQL 15+ with connection pooling
- **Cache**: Redis 7+ for sessions and caching
- **Authentication**: JWT with refresh tokens
- **WebSockets**: Real-time sync and notifications
- **Containerization**: Docker with multi-stage builds

### Project Structure
```
auto_parts_erp_backend/
├── bin/
│   ├── server.dart              # Main server entry point
│   └── migrate.dart             # Database migration tool
├── lib/
│   ├── api/                     # API route handlers
│   │   ├── auth/                # Authentication endpoints
│   │   ├── products/            # Product management
│   │   ├── inventory/           # Inventory operations
│   │   ├── sales/               # Sales and POS
│   │   ├── shared_economy/      # B2B marketplace
│   │   └── sync/                # Data synchronization
│   ├── models/                  # Data models and DTOs
│   ├── services/                # Business logic services
│   │   ├── database/            # Database service
│   │   ├── auth/                # Authentication service
│   │   ├── cache/               # Redis cache service
│   │   ├── sync/                # Synchronization service
│   │   └── notifications/       # WebSocket notifications
│   └── utils/                   # Utility functions
├── docker-compose.yml           # Multi-service orchestration
├── Dockerfile                   # Production container
├── nginx.conf                   # Reverse proxy configuration
└── init.sql                     # Database initialization
```

## 🔧 Configuration

### Environment Variables

```bash
# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/auto_parts_erp
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=auto_parts_erp
DATABASE_USER=auto_parts_user
DATABASE_PASSWORD=your_secure_password

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# JWT Configuration
JWT_SECRET=your_very_secure_jwt_secret_key
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Server Configuration
SERVER_PORT=8080
SERVER_HOST=0.0.0.0
ENVIRONMENT=production

# CORS Configuration
CORS_ORIGINS=*
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=Content-Type,Authorization

# WhatsApp Business API (Optional)
WHATSAPP_API_URL=https://graph.facebook.com/v18.0
WHATSAPP_ACCESS_TOKEN=your_whatsapp_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id

# ZATCA Configuration (Saudi Arabia)
ZATCA_ENVIRONMENT=sandbox
ZATCA_API_URL=https://gw-fatoora.zatca.gov.sa
ZATCA_USERNAME=your_zatca_username
ZATCA_PASSWORD=your_zatca_password
```

## 📊 Database Schema

### Core Tables
- **companies** - Multi-tenant company data
- **branches** - Company branch locations
- **users** - User accounts and permissions
- **products** - Product catalog with multilingual support
- **product_oem_numbers** - Multiple OEM numbers per product
- **car_compatibility** - Vehicle compatibility matrix
- **inventory** - Multi-branch stock management
- **customers** - Customer database
- **suppliers** - Supplier management
- **sales** - Sales transactions
- **sale_items** - Sale line items
- **shared_inventory** - B2B marketplace listings
- **sync_queue** - Offline operation synchronization

### Key Features
- **UUID primary keys** for distributed systems
- **JSONB columns** for flexible metadata
- **Full-text search** with PostgreSQL's tsvector
- **Trigram indexes** for fuzzy search
- **Automatic timestamps** with triggers
- **Multi-tenant isolation** by company_id

## 🔐 API Endpoints

### Authentication
```
POST   /api/auth/login           # User login
POST   /api/auth/refresh         # Refresh JWT token
POST   /api/auth/logout          # User logout
GET    /api/auth/profile         # Get user profile
PUT    /api/auth/profile         # Update user profile
```

### Products
```
GET    /api/products             # List products
POST   /api/products             # Create product
GET    /api/products/:id         # Get product details
PUT    /api/products/:id         # Update product
DELETE /api/products/:id         # Delete product
GET    /api/products/search      # Search products
GET    /api/products/:id/oem     # Get OEM numbers
POST   /api/products/:id/oem     # Add OEM number
```

### Inventory
```
GET    /api/inventory            # List inventory
POST   /api/inventory/transfer   # Transfer stock
PUT    /api/inventory/:id        # Update stock levels
GET    /api/inventory/low-stock  # Get low stock alerts
POST   /api/inventory/count      # Stock count operation
```

### Sales
```
GET    /api/sales               # List sales
POST   /api/sales               # Create sale
GET    /api/sales/:id           # Get sale details
PUT    /api/sales/:id           # Update sale
POST   /api/sales/:id/print     # Generate invoice
POST   /api/sales/:id/whatsapp  # Send via WhatsApp
```

### Shared Economy
```
GET    /api/shared/marketplace   # Browse marketplace
POST   /api/shared/listings      # Create listing
GET    /api/shared/partners      # List partners
POST   /api/shared/quotes        # Request quote
```

### Sync
```
GET    /api/sync/status          # Get sync status
POST   /api/sync/push            # Push local changes
GET    /api/sync/pull            # Pull remote changes
POST   /api/sync/resolve         # Resolve conflicts
```

## 🔄 Real-time Features

### WebSocket Endpoints
```
/ws/notifications               # General notifications
/ws/inventory                   # Inventory updates
/ws/sales                       # Sales notifications
/ws/sync                        # Sync status updates
```

### Event Types
- **inventory.updated** - Stock level changes
- **sale.created** - New sale notification
- **sync.status** - Synchronization updates
- **user.online** - User presence updates

## 🚀 Deployment

### Production Deployment

1. **Configure environment**
   ```bash
   # Copy and edit production environment
   cp .env.example .env.production
   ```

2. **Deploy with Docker Compose**
   ```bash
   docker-compose -f docker-compose.yml up -d
   ```

3. **Set up SSL (recommended)**
   ```bash
   # Update nginx.conf with SSL configuration
   # Place SSL certificates in ./ssl/ directory
   ```

4. **Configure domain and firewall**
   ```bash
   # Point domain to server IP
   # Open ports 80, 443
   # Configure firewall rules
   ```

### Health Monitoring

```bash
# Check service health
curl http://your-domain.com/health

# Monitor logs
docker-compose logs -f api

# Database health
docker-compose exec postgres pg_isready

# Redis health
docker-compose exec redis redis-cli ping
```

## 🧪 Testing

### Run Tests
```bash
# Unit tests
dart test

# Integration tests
dart test test/integration/

# Load testing
dart test test/load/
```

### API Testing
```bash
# Using curl
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password"}'

# Using Postman collection
# Import postman/auto_parts_erp.postman_collection.json
```

## 📈 Performance

### Optimization Features
- **Connection pooling** for PostgreSQL
- **Redis caching** for frequently accessed data
- **Gzip compression** via Nginx
- **Rate limiting** to prevent abuse
- **Database indexing** for fast queries
- **Lazy loading** for large datasets

### Monitoring
- **Health check endpoints** for uptime monitoring
- **Structured logging** with JSON format
- **Performance metrics** via custom middleware
- **Database query optimization** with EXPLAIN ANALYZE

## 🔒 Security

### Security Features
- **JWT authentication** with secure token storage
- **Password hashing** with bcrypt
- **Rate limiting** on sensitive endpoints
- **CORS configuration** for cross-origin requests
- **SQL injection prevention** with parameterized queries
- **Input validation** and sanitization
- **HTTPS enforcement** in production

### Best Practices
- Regular security updates
- Environment variable secrets
- Database connection encryption
- API versioning for backward compatibility
- Audit logging for sensitive operations

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Write tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- 📧 Email: <EMAIL>
- 📖 API Documentation: [api-docs.autopartserp.com](https://api-docs.autopartserp.com)
- 🐛 Issues: [GitHub Issues](https://github.com/your-repo/auto_parts_erp_backend/issues)

---

**Built with ❤️ using Dart and modern cloud technologies**
