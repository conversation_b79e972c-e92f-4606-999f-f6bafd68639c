name: auto_parts_erp_backend
description: Backend API server for Auto Parts ERP system
version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  # Web framework
  shelf: ^1.4.1
  shelf_router: ^1.1.4
  shelf_cors_headers: ^0.1.5
  shelf_static: ^1.1.2
  shelf_web_socket: ^1.0.4
  
  # Database
  postgres: ^2.6.2
  redis: ^3.1.0
  
  # Authentication & Security
  dart_jsonwebtoken: ^2.12.2
  crypto: ^3.0.3
  bcrypt: ^1.1.3
  
  # HTTP & Networking
  http: ^1.2.1
  dio: ^5.4.3+1
  
  # JSON & Serialization
  json_annotation: ^4.9.0
  
  # Utilities
  uuid: ^4.4.2
  intl: ^0.20.2
  logger: ^2.4.0
  dotenv: ^4.2.0
  
  # File handling
  mime: ^1.0.5
  path: ^1.9.0
  
  # Validation
  validators: ^3.0.0

dev_dependencies:
  # Testing
  test: ^1.25.8
  mockito: ^5.4.4
  
  # Code generation
  build_runner: ^2.4.12
  json_serializable: ^6.8.0
  
  # Linting
  lints: ^4.0.0
