version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: auto_parts_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: auto_parts_erp
      POSTGRES_USER: auto_parts_user
      POSTGRES_PASSWORD: auto_parts_password_2024
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - auto_parts_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U auto_parts_user -d auto_parts_erp"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: auto_parts_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - auto_parts_network
    command: redis-server --appendonly yes --requirepass redis_password_2024
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Auto Parts ERP Backend API
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: auto_parts_api
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      # Database Configuration
      DATABASE_URL: *******************************************************************/auto_parts_erp
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_NAME: auto_parts_erp
      DATABASE_USER: auto_parts_user
      DATABASE_PASSWORD: auto_parts_password_2024
      
      # Redis Configuration
      REDIS_URL: redis://:redis_password_2024@redis:6379/0
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: redis_password_2024
      
      # JWT Configuration
      JWT_SECRET: auto_parts_jwt_secret_key_2024_very_secure
      JWT_EXPIRES_IN: 24h
      JWT_REFRESH_EXPIRES_IN: 7d
      
      # Server Configuration
      SERVER_PORT: 8080
      SERVER_HOST: 0.0.0.0
      ENVIRONMENT: production
      
      # CORS Configuration
      CORS_ORIGINS: "*"
      CORS_METHODS: "GET,POST,PUT,DELETE,OPTIONS"
      CORS_HEADERS: "Content-Type,Authorization"
      
      # File Upload Configuration
      MAX_FILE_SIZE: 10MB
      UPLOAD_PATH: /app/uploads
      
      # Logging Configuration
      LOG_LEVEL: info
      LOG_FORMAT: json
      
      # WhatsApp Business API (Optional)
      WHATSAPP_API_URL: https://graph.facebook.com/v18.0
      WHATSAPP_ACCESS_TOKEN: your_whatsapp_access_token
      WHATSAPP_PHONE_NUMBER_ID: your_phone_number_id
      
      # ZATCA Configuration (Saudi Arabia)
      ZATCA_ENVIRONMENT: sandbox
      ZATCA_API_URL: https://gw-fatoora.zatca.gov.sa
      ZATCA_USERNAME: your_zatca_username
      ZATCA_PASSWORD: your_zatca_password
      
    volumes:
      - api_uploads:/app/uploads
      - api_logs:/app/logs
    networks:
      - auto_parts_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: auto_parts_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    networks:
      - auto_parts_network
    depends_on:
      - api
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # pgAdmin (Database Management - Development Only)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: auto_parts_pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: pgadmin_password_2024
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - auto_parts_network
    depends_on:
      - postgres
    profiles:
      - development

  # Redis Commander (Redis Management - Development Only)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: auto_parts_redis_commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379:0:redis_password_2024
    ports:
      - "8081:8081"
    networks:
      - auto_parts_network
    depends_on:
      - redis
    profiles:
      - development

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  api_uploads:
    driver: local
  api_logs:
    driver: local
  nginx_logs:
    driver: local
  pgadmin_data:
    driver: local

networks:
  auto_parts_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
