claude-code "Create an advanced Warp 2.0 style sidebar navigation system for Flutter Desktop ERP application with the following specifications:

PROJECT: Advanced Sidebar Navigation for auto_parts_erp

DESIGN CONCEPT: Warp 2.0 Aesthetic
- Futuristic design with glass morphism effects
- Neon accent colors with subtle glow effects
- Smooth hover expand animations (side-to-side expansion)
- Multi-level navigation with elegant transitions
- Collapsed state shows only icons, expanded shows full menu
- Sub-menus slide out horizontally on hover

TECHNICAL REQUIREMENTS:

1. SIDEBAR BEHAVIOR:
   - Default width: 70px (collapsed with icons only)
   - Expanded width: 280px (on hover or click)
   - Sub-menu panel width: 250px (slides from right edge)
   - Animation duration: 300ms with custom curves
   - Mouse region detection for auto-expand
   - Keyboard navigation support (Tab, Arrow keys)
   - Remember user preference (collapsed/expanded)

2. VISUAL EFFECTS:
   ```dart
   // Glass morphism container
   Container(
     decoration: BoxDecoration(
       gradient: LinearGradient(
         begin: Alignment.topLeft,
         end: Alignment.bottomRight,
         colors: [
           Colors.white.withOpacity(0.1),
           Colors.white.withOpacity(0.05),
         ],
       ),
       borderRadius: BorderRadius.circular(20),
       border: Border.all(
         color: Colors.white.withOpacity(0.2),
         width: 1.5,
       ),
       boxShadow: [
         BoxShadow(
           color: Colors.black.withOpacity(0.1),
           blurRadius: 20,
           offset: Offset(0, 10),
         ),
       ],
     ),
     child: ClipRRect(
       borderRadius: BorderRadius.circular(20),
       child: BackdropFilter(
         filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
         child: // Sidebar content
       ),
     ),
   )
   ```

3. MENU STRUCTURE:
   ```dart
   class MenuItem {
     final String id;
     final String titleAr;
     final String titleEn;
     final IconData icon;
     final Color? iconColor;
     final List<MenuItem>? subItems;
     final String? route;
     final bool showBadge;
     final int badgeCount;
     final bool isPremium;
   }
   ```

4. ANIMATION SPECIFICATIONS:
   - Icon rotation on hover: 360° spin
   - Menu item scale on hover: 1.05x
   - Glow effect intensity animation
   - Stagger animation for sub-items appearance
   - Ripple effect on click
   - Smooth color transitions

5. COLOR SCHEME:
   ```dart
   // Warp 2.0 Color Palette
   class WarpColors {
     static const primary = Color(0xFF00D4FF);      // Cyan
     static const secondary = Color(0xFFFF006E);    // Pink
     static const accent = Color(0xFF8B5CF6);       // Purple
     static const success = Color(0xFF10B981);      // Green
     static const warning = Color(0xFFF59E0B);      // Amber
     static const danger = Color(0xFFEF4444);       // Red
     
     // Glass effect colors
     static const glassWhite = Color(0x1AFFFFFF);
     static const glassBlack = Color(0x1A000000);
     static const borderGlow = Color(0x4D00D4FF);
   }
   ```

6. FEATURES TO IMPLEMENT:

   a) Smart Hover System:
      - Detect mouse proximity for pre-expand hints
      - Smooth follow cursor for glow effects
      - Intelligent sub-menu positioning
      - Auto-collapse when mouse leaves area

   b) Search Integration:
      - Spotlight search with CMD/CTRL + K
      - Fuzzy search through all menu items
      - Animated search results
      - Voice search ready (future feature)

   c) Quick Actions:
      - Right-click context menus
      - Drag to reorder favorites
      - Pin frequently used items
      - Keyboard shortcuts display

   d) Responsive Behavior:
      - Adapt to window resize
      - Touch-friendly for touchscreen monitors
      - High DPI support
      - Multi-monitor awareness

7. ACCESSIBILITY:
   - Screen reader support
   - High contrast mode
   - Keyboard-only navigation
   - Focus indicators
   - Tooltip descriptions

8. PERFORMANCE:
   - Use RepaintBoundary for animations
   - Implement lazy loading for sub-menus
   - Cache rendered glassmorphism effects
   - Optimize for 144Hz displays

CREATE THE FOLLOWING FILES:

1. lib/core/widgets/warp_sidebar/warp_sidebar.dart
   - Main sidebar widget with all animations

2. lib/core/widgets/warp_sidebar/menu_item_widget.dart
   - Individual menu item with hover effects

3. lib/core/widgets/warp_sidebar/submenu_panel.dart
   - Sliding submenu panel widget

4. lib/core/widgets/warp_sidebar/glass_container.dart
   - Reusable glassmorphism container

5. lib/core/widgets/warp_sidebar/warp_animations.dart
   - Custom animation controllers and curves

6. lib/core/widgets/warp_sidebar/sidebar_controller.dart
   - State management for sidebar using Riverpod

7. lib/core/theme/warp_theme.dart
   - Warp 2.0 design system theme

8. Example implementation with all menu items from the ERP system

SAMPLE MENU ITEMS:
- Dashboard (with live data indicators)
- Sales (with pending orders badge)
- Inventory (with low stock alerts)
- Finance (with today's revenue)
- Reports (with new reports indicator)
- Settings (with sync status)

Make it production-ready with smooth 60+ FPS animations and stunning visual effects that would impress in a modern desktop application. Include RTL support for Arabic with proper animations mirroring."